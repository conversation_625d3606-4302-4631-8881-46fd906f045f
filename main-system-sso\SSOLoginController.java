package com.ruoyi.auth.controller;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.auth.service.SSOAuthCenterService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.service.TokenService;
import com.ruoyi.system.api.model.LoginUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

/**
 * SSO登录页面控制器
 * 处理用户登录页面和SSO跳转逻辑
 * 
 * <AUTHOR>
 */
@Controller
@RequestMapping("/sso")
public class SSOLoginController {

    private static final Logger log = LoggerFactory.getLogger(SSOLoginController.class);

    @Autowired
    private SSOAuthCenterService ssoAuthCenterService;

    @Autowired
    private TokenService tokenService;

    /**
     * SSO登录页面
     * 显示统一登录页面，支持跳转到不同系统
     */
    @GetMapping("/login")
    public String loginPage(@RequestParam(value = "target", required = false) String targetSystem,
                           @RequestParam(value = "redirect", required = false) String redirectUrl,
                           Model model) {
        
        log.info("访问SSO登录页面 - 目标系统: {}, 重定向: {}", targetSystem, redirectUrl);
        
        // 传递参数到前端页面
        model.addAttribute("targetSystem", targetSystem);
        model.addAttribute("redirectUrl", redirectUrl);
        
        // 获取已注册的系统列表
        model.addAttribute("registeredSystems", getRegisteredSystemsList());
        
        return "sso/login"; // 返回登录页面模板
    }

    /**
     * 处理登录表单提交
     */
    @PostMapping("/doLogin")
    @ResponseBody
    public R<?> doLogin(@RequestBody JSONObject loginForm) {
        try {
            String username = loginForm.getString("username");
            String password = loginForm.getString("password");
            String targetSystem = loginForm.getString("targetSystem");
            String redirectUrl = loginForm.getString("redirectUrl");

            log.info("处理SSO登录 - 用户: {}, 目标系统: {}", username, targetSystem);

            // 1. 验证用户凭据
            LoginUser loginUser = ssoAuthCenterService.authenticate(username, password);
            if (loginUser == null) {
                return R.fail("用户名或密码错误");
            }

            // 2. 检查用户对目标系统的访问权限
            if (StringUtils.isNotEmpty(targetSystem)) {
                boolean hasPermission = ssoAuthCenterService.checkSystemPermission(loginUser, targetSystem);
                if (!hasPermission) {
                    return R.fail("您无权限访问该系统");
                }
            }

            // 3. 生成SSO Token
            String ssoToken = ssoAuthCenterService.generateSSOToken(loginUser, targetSystem);
            if (StringUtils.isEmpty(ssoToken)) {
                return R.fail("生成SSO Token失败");
            }

            // 4. 生成主系统访问Token
            Map<String, Object> tokenMap = tokenService.createToken(loginUser);

            // 5. 记录登录日志
            ssoAuthCenterService.recordSSOLogin(loginUser, targetSystem, ssoToken);

            // 6. 构造返回结果
            JSONObject result = new JSONObject();
            result.put("ssoToken", ssoToken);
            result.put("accessToken", tokenMap.get("access_token"));
            result.put("expiresIn", tokenMap.get("expires_in"));
            result.put("user", loginUser);

            // 如果指定了目标系统，构造跳转URL
            if (StringUtils.isNotEmpty(targetSystem)) {
                String jumpUrl = ssoAuthCenterService.buildTargetSystemUrl(targetSystem, ssoToken, redirectUrl);
                result.put("jumpUrl", jumpUrl);
                result.put("needJump", true);
            } else {
                result.put("needJump", false);
            }

            return R.ok("登录成功", result);

        } catch (Exception e) {
            log.error("SSO登录处理失败", e);
            return R.fail("登录失败: " + e.getMessage());
        }
    }

    /**
     * 系统选择页面
     * 用户登录成功后选择要访问的系统
     */
    @GetMapping("/selectSystem")
    public String selectSystemPage(@RequestParam String ssoToken, Model model) {
        try {
            // 验证SSO Token
            LoginUser loginUser = ssoAuthCenterService.validateSSOToken(ssoToken);
            if (loginUser == null) {
                return "redirect:/sso/login?error=token_invalid";
            }

            model.addAttribute("ssoToken", ssoToken);
            model.addAttribute("user", loginUser);
            model.addAttribute("registeredSystems", getRegisteredSystemsList());

            return "sso/selectSystem";

        } catch (Exception e) {
            log.error("系统选择页面加载失败", e);
            return "redirect:/sso/login?error=system_error";
        }
    }

    /**
     * 跳转到指定系统
     */
    @GetMapping("/jumpTo/{systemId}")
    public void jumpToSystem(@PathVariable String systemId,
                            @RequestParam String ssoToken,
                            @RequestParam(required = false) String redirectUrl,
                            HttpServletResponse response) throws IOException {
        try {
            // 验证SSO Token
            LoginUser loginUser = ssoAuthCenterService.validateSSOToken(ssoToken);
            if (loginUser == null) {
                response.sendRedirect("/sso/login?error=token_invalid");
                return;
            }

            // 检查系统访问权限
            boolean hasPermission = ssoAuthCenterService.checkSystemPermission(loginUser, systemId);
            if (!hasPermission) {
                response.sendRedirect("/sso/login?error=no_permission");
                return;
            }

            // 构造跳转URL
            String jumpUrl = ssoAuthCenterService.buildTargetSystemUrl(systemId, ssoToken, redirectUrl);
            
            log.info("用户 {} 跳转到系统 {}: {}", loginUser.getUsername(), systemId, jumpUrl);
            
            response.sendRedirect(jumpUrl);

        } catch (Exception e) {
            log.error("跳转到系统失败", e);
            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "跳转失败");
        }
    }

    /**
     * SSO登出页面
     */
    @GetMapping("/logout")
    public String logoutPage(@RequestParam(required = false) String ssoToken,
                            @RequestParam(required = false) String returnUrl,
                            Model model) {
        try {
            if (StringUtils.isNotEmpty(ssoToken)) {
                // 执行SSO登出
                LoginUser loginUser = ssoAuthCenterService.validateSSOToken(ssoToken);
                if (loginUser != null) {
                    ssoAuthCenterService.clearSSOToken(ssoToken);
                    ssoAuthCenterService.notifyAllSystemsLogout(loginUser.getUserId().toString(), ssoToken);
                    ssoAuthCenterService.recordSSOLogout(loginUser.getUserId().toString(), ssoToken);
                }
            }

            model.addAttribute("returnUrl", returnUrl);
            return "sso/logout";

        } catch (Exception e) {
            log.error("SSO登出失败", e);
            return "sso/error";
        }
    }

    /**
     * 获取已注册系统列表
     */
    private JSONObject getRegisteredSystemsList() {
        JSONObject systems = new JSONObject();
        
        // 这里可以从配置或数据库中获取系统列表
        JSONObject marketSystem = new JSONObject();
        marketSystem.put("id", "market");
        marketSystem.put("name", "智能市场系统");
        marketSystem.put("description", "智能化的市场交易平台");
        marketSystem.put("icon", "el-icon-shopping");
        
        systems.put("market", marketSystem);
        
        return systems;
    }

    /**
     * 错误页面
     */
    @GetMapping("/error")
    public String errorPage(@RequestParam(required = false) String message, Model model) {
        model.addAttribute("errorMessage", message);
        return "sso/error";
    }
}
