package com.ruoyi.shop.enums.user;

/**
 * 用户类型
 */
public enum UserType {

    ADMIN("ADMIN","管理员"),
    STAFF("STAFF","员工");

    private final String key;
    private final String value;

    UserType(String key, String value){
        this.key = key;
        this.value = value;
    }
    //根据key获取枚举
    public static UserType getUserType(String key){
        if(null == key){
            return null;
        }
        for(UserType temp: UserType.values()){
            if(temp.getKey().equals(key)){
                return temp;
            }
        }
        return null;
    }

    public String getKey() {
        return key;
    }
    public String getValue() {
        return value;
    }
}
