package com.ruoyi.shop.feign.order;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.shop.entity.Order;
import com.ruoyi.shop.entity.OrderDeliver;
import com.ruoyi.shop.model.OrderModel;
import com.ruoyi.shop.result.QueryResult; import com.ruoyi.shop.result.Result;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author:micah
 **/

public interface OrderFeign {

    /***
     * Order分页条件搜索实现
     * @param order
     * @param page
     * @param size
     * @return
     */
    @PostMapping(value = "/order/search/{page}/{size}" )
    Result<QueryResult<Order>> findPage(@RequestBody(required = false) Order order, @PathVariable("page") int page, @PathVariable("size") int size, @RequestParam(value = "fields", required = false) String fields);

    @PostMapping(value = "/order/orderFilesList/{page}/{size}" )
    Result<List<Order>> orderFilesList(@RequestBody(required = false) Order order, @PathVariable("page") int page, @PathVariable("size") int size, @RequestParam(value = "fields", required = false) String fields);
    /***
     * 多条件搜索数据
     * @param order
     * @return
     */
    @PostMapping(value = "/order/search" )
    Result<List<Order>> findList(@RequestBody(required = false) Order order, @RequestParam(value = "fields", required = false) String fields);

    /***
     * 根据ID删除数据
     * @param id
     * @return
     */
    @DeleteMapping(value = "/order/{id}" )
    Result<Boolean> delete(@PathVariable("id") Long id);

    /***
     * 修改Order数据
     * @param order
     * @param
     * @return
     */
    @PutMapping(value="/order/update")
    Result<Boolean> update(@RequestBody Order order);

    /***
     * 新增Order数据
     * @param orderModel
     * @return
     */
    @PostMapping(value="/order/add")
    Result<Boolean> add(@RequestBody OrderModel orderModel);

    /***
     * 批量新增Order数据
     * @param orders
     * @return
     */
    @PostMapping("/order/batch/add")
    Result<Boolean> batchAdd(@RequestBody List<OrderModel> orders);

    /***
     * 批量刪除Order数据
     * @param opid
     * @return
     */
    @PostMapping("/order/batch/delete")
    Result<Boolean> batchDelete(@RequestParam("opid") String opid);

    /***
     * 批量状态Order数据
     * @param opid
     * @return
     */
    @PostMapping("/order/state/op")
    Result<Boolean> stateOp(@RequestParam("opid") String opid, @RequestParam("status") String status);

    /***
     * 根据ID查询Order数据
     * @param id
     * @return
     */
    @GetMapping("/order/{id}")
    Result<Order> findById(@PathVariable("id") Long id);


    /**
     * 根据ID查询信息
     *
     * @param ids
     * @return
     */
    @GetMapping("/order/ids")
    Result<List<Order>> findByIds(@RequestParam("ids") String ids, @RequestParam(value = "fields", required = false) String fields);

    /**
     * 未完成订单数量
     * @param enterprise_id
     * @return
     */
    @GetMapping("/order/number/going")
    Result<Integer> findGoingOrder(@RequestParam("enterprise_id") Long enterprise_id);

    /**
     * 取消订单
     * @param id
     * @return
     */
    @PostMapping("/order/cancel/op")
    Result<Boolean> cancelOrder(@RequestParam("id") Long id);

    /**
     * 数据统计
     * @param enterpriseId
     * @return
     */
    @PostMapping("/order/date/show")
    Result<JSONObject> statistics(@RequestParam("enterprise_id") Long enterpriseId,@RequestParam("type") String type);



    /**
     * 添加订单文件
     * @param id
     * @return
     */
    @PostMapping("/order/addFile")
    Result<JSONObject> addFile(@RequestParam("id") Long id,@RequestParam("file_name") String file_name,@RequestParam("file_url") String file_url);

}