package com.ruoyi.shop.feign.enterprise;

import com.ruoyi.shop.result.QueryResult; import com.ruoyi.shop.result.Result;
import com.ruoyi.shop.entity.EnterpriseApplication;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * @Author:micah
 **/
public interface EnterpriseApplicationFeign {

    /***
     * EnterpriseApplication分页条件搜索实现
     * @param enterpriseApplication
     * @param page
     * @param size
     * @return
     */
    @PostMapping(value = "/enterprise/application/search/{page}/{size}" )
    Result<QueryResult<EnterpriseApplication>> findPage(@RequestBody(required = false) EnterpriseApplication enterpriseApplication, @PathVariable("page") int page, @PathVariable("size") int size, @RequestParam(value = "fields", required = false) String fields);

    /***
     * 多条件搜索数据
     * @param enterpriseApplication
     * @return
     */
    @PostMapping(value = "/enterprise/application/search" )
    Result<List<EnterpriseApplication>> findList(@RequestBody(required = false) EnterpriseApplication enterpriseApplication, @RequestParam(value = "fields", required = false) String fields);
}