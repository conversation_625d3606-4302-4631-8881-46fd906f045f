package com.ruoyi.shop.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.lang.Long;
import java.util.Date;
import java.lang.String;

/**
 * @Author:micah
 **/

@TableName("tb_message")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Message extends Model<Message>{

	@TableId(value = "id", type = IdType.AUTO)
	private Long id;//主键id

	private Long enterprise_id;//企业ID

	private Long user_id;//用户ID

	private String type;//消息类型

	private String is_read;//0 未读  1 已读

	private String target_id;//业务id

	private Date create_time;//创建时间

	private String title;//消息标题

	private String content;//内容

	@TableField(exist = false)
	private String userType;//purchase 采购商  supply 供应商
}
