package com.ruoyi.auth.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * SSO配置类
 * 
 * <AUTHOR>
 */
@Configuration
public class SSOConfig {

    @Value("${rest-template.connect-timeout:5000}")
    private int connectTimeout;

    @Value("${rest-template.read-timeout:10000}")
    private int readTimeout;

    /**
     * 配置RestTemplate用于SSO接口调用
     */
    @Bean
    public RestTemplate restTemplate() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(connectTimeout);
        factory.setReadTimeout(readTimeout);
        
        return new RestTemplate(factory);
    }
}
