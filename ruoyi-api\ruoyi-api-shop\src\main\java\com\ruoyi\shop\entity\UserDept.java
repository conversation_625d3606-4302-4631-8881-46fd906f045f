package com.ruoyi.shop.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:micah
 **/

@TableName("tb_user_dept")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserDept extends Model<UserDept>{

	@TableId(value = "id", type = IdType.AUTO)
	private Long id;// 主键

	private Long user_id;//部门id

	private Long dept_id;//部门ID


}
