package com.ruoyi.shop.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Author:micah
 **/

@TableName("tb_store")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Store extends Model<Store>{

	@TableId(value = "id", type = IdType.AUTO)
	private Long id;//主键id

	private String logo;//店铺Logo

	private Long enterprise_id;//公司id

	private String name;//店铺名称

	private String banner;//首页顶部banner

	private String banner2;//最新顶部Banner

	private String status;//状态 WAIT-待审核 OPEN-已通过 DENY-已驳回 CLOSE-已关店

	private String openbank;//开户行

	private String cardno;//银行账户

	private String account;//户头

	private String bank;//开户地址

	private String certfile;//认证文件

	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String remark;//备注

	private Date create_time;//创建时间

	private String create_by;//创建人

	private Date update_time;//更新时间

	private String update_by;//更新人

	private String ali_mchid;
	/**
	 * 微信直连商户号
	 * */
	private String wx_mchid;

	private String fitment_status;//店铺装修状态，1-待审核，2-已审核,3-已驳回

	private String rejection_reason;//驳回原因

	private String content;//内容

}
