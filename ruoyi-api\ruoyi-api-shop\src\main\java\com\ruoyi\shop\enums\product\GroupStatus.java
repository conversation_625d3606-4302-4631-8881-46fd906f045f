package com.ruoyi.shop.enums.product;/**
 * @ProjectName: ruoyi
 * @Package: com.ruoyi.shop.enums.product
 * @ClassName: CentralStatus
 * @Author: ${maguojun}
 * @Description:
 * @Date: 2022/5/18 9:50
 * @Version: 1.0
 */

/**
 * @program: ruoyi
 *
 * @description:
 *
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 *
 * @create: 2022-05-18 09:50
 **/
public enum GroupStatus {

    GOING("GOING","进行中"),
    STOP("STOP","已停止"),
    DONE("DONE","已结束"),
    CANCEL("CANCEL","已取消");

    private final String key;
    private final String value;

    GroupStatus(String key, String value){
        this.key = key;
        this.value = value;
    }
    //根据key获取枚举
    public static GroupStatus getGroupStatus(String key){
        if(null == key){
            return null;
        }
        for(GroupStatus temp: GroupStatus.values()){
            if(temp.getKey().equals(key)){
                return temp;
            }
        }
        return null;
    }

    public String getKey() {
        return key;
    }
    public String getValue() {
        return value;
    }
}
