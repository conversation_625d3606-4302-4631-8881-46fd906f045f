package com.ruoyi.shop.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @ClassName Scene
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/1/5
 * @Version 1.0
 */
@TableName("tb_scene")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Scene extends Model<Scene> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;//ID

    private String scene_name;//场景名称

    private Long scene_sort;//排序

    private String scene_pic;//场景展示图

    private String recommend;//首页推荐  1不推荐 2推荐

    private String status;//状态

    private String create_by;//创建者

    private Date create_time;//创建时间

    private String update_by;//更新者

    private Date update_time;//更新时间
}
