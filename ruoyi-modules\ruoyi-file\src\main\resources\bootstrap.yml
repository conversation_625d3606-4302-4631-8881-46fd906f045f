# Tomcat
server:
  port: 9300

# Spring
spring:
  application:
    # 应用名称
    name: ruoyi-file
  profiles:
    # 环境配置
    active: dev
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 127.0.0.1:8848
        username: nacos
        password: nacos
        namespace: ca6f4dcd-0826-4ad7-9eff-9180f9a832e1
      config:
        # 配置中心地址
        server-addr: 127.0.0.1:8848
        username: nacos
        password: nacos
        namespace: ca6f4dcd-0826-4ad7-9eff-9180f9a832e1
        file-extension: yaml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
