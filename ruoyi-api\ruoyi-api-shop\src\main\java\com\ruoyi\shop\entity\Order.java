package com.ruoyi.shop.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author:micah
 **/

@TableName("tb_order")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Order extends Model<Order>{

	@TableId(value = "id", type = IdType.AUTO)
	private Long id;//主键id

	private Long offer_id;//报价ID

	private String order_no;//订单编号

	private String order_type;//订单类型 NORMAL-普通 GROUP-团购 CENTRAL-集采

	private String order_fetch;//取货方式 SELF-自提 DELIVER-快递

	private Long central_product;//集采商品

	private String payment;//付款方式

	private String linker;

	private String linkphone;

	private String province;//省份

	private String city;//城市

	private String region;//区域

	private String address;//详细地址

	private String contract_no;//合同编号

	private String supply_name;//供方

	private Long supply_id;//供方id

	private String supply_phone;//供方手机号

	private String supply_location;//供方地址

	private String demand_location;//需方地址

	private String supply_proxy;//供方委托代理人

	private String demand_name;//需方

	private Long demand_id;//需方id

	private String demand_phone;//需方手机号

	private String demand_proxy;//需方委托代理人

	private String terms;//订单条款

	private String remark;//备注

	private String status;//状态，NEW-审核中，CONFIRM-待（供应商）确认，WAITPAY待付款，PAYCONFIRM付款(供应商)待确认，WAITSHIP待发货，PARTSHIP部分发货，

	private String central_status;//集采状态

	private BigDecimal deposit;//集采订金

	private BigDecimal total_number;//商品总数

	private BigDecimal shiped_number;//发货数

	private BigDecimal total_price;//订单总金额

	private String logistics_no;//物流单号

	private String central_pay_status;//集采支付状态

	private Date create_time;//创建时间

	private String create_by;//创建人

	private Date update_time;//更新时间

	private String update_by;//更新人

	private String operator;//操作员

	@TableField(exist = false)
	private String source;//来源

	@TableField(exist = false)
	private String startTime;//起始时间

	@TableField(exist = false)
	private String endTime;//结束时间

	@TableField(exist = false)
	private String telPhone;//登录人手机号

	private String after_status;//售后状态  0-未申请 1-已申请


}
