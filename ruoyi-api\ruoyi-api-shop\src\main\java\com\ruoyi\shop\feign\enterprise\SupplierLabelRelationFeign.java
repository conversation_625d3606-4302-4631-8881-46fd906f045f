package com.ruoyi.shop.feign.enterprise;

import com.ruoyi.shop.entity.SupplierLabel;
import com.ruoyi.shop.entity.SupplierLabelRelation;
import com.ruoyi.shop.result.QueryResult;
import com.ruoyi.shop.result.Result;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author:micah
 **/
public interface SupplierLabelRelationFeign {

    /***
     * SupplierLabelRelation分页条件搜索实现
     * @param supplierLabelRelation
     * @param page
     * @param size
     * @return
     */
    @PostMapping(value = "/supplierLabelRelation/search/{page}/{size}" )
    Result<QueryResult<SupplierLabelRelation>> findPage(@RequestBody(required = false) SupplierLabelRelation supplierLabelRelation, @PathVariable("page") int page, @PathVariable("size") int size, @RequestParam(value = "fields", required = false) String fields);

    /***
     * 多条件搜索数据
     * @param supplierLabelRelation
     * @return
     */
    @PostMapping(value = "/supplierLabelRelation/search" )
    Result<List<SupplierLabelRelation>> findList(@RequestBody(required = false) SupplierLabelRelation supplierLabelRelation, @RequestParam(value = "fields", required = false) String fields);

    /***
     * 根据ID删除数据
     * @param id
     * @return
     */
    @DeleteMapping(value = "/supplierLabelRelation/{id}" )
    Result<Boolean> delete(@PathVariable("id") Long id);

    /***
     * 修改SupplierLabelRelation数据
     * @param supplierLabelRelation
     * @param id
     * @return
     */
    @PutMapping(value="/supplierLabelRelation/{id}")
    Result<Boolean> update(@RequestBody SupplierLabelRelation supplierLabelRelation, @PathVariable("id") Long id);

    /***
     * 新增SupplierLabelRelation数据
     * @param supplierLabelRelation
     * @return
     */
    @PostMapping("/supplierLabelRelation")
    Result<Boolean> add(@RequestBody SupplierLabelRelation supplierLabelRelation);

    /***
     * 批量新增SupplierLabelRelation数据
     * @param supplierLabelRelations
     * @return
     */
    @PostMapping("/supplierLabelRelation/batch/add")
    Result<Boolean> batchAdd(@RequestBody List<SupplierLabelRelation> supplierLabelRelations);

    /***
     * 批量刪除SupplierLabelRelation数据
     * @param opid
     * @return
     */
    @PostMapping("/supplierLabelRelation/batch/delete")
    Result<Boolean> batchDelete(@RequestParam("opid") String opid);

    /***
     * 批量状态SupplierLabelRelation数据
     * @param opid
     * @return
     */
    @PostMapping("/supplierLabelRelation/state/op")
    Result<Boolean> stateOp(@RequestParam("opid") String opid, @RequestParam("status") Integer status);

    /***
     * 根据ID查询SupplierLabelRelation数据
     * @param id
     * @return
     */
    @GetMapping("/supplierLabelRelation/{id}")
    Result<SupplierLabelRelation> findById(@PathVariable("id") Long id);

    @PostMapping(value = "/supplierLabelRelation/search/user" )
    Result<List<SupplierLabel>> queryListBySupplier(@RequestParam("supplierId") Long supplierId);
}