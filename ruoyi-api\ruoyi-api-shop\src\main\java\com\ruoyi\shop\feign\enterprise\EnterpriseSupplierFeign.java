package com.ruoyi.shop.feign.enterprise;

import com.ruoyi.shop.entity.Enterprise;
import com.ruoyi.shop.entity.EnterpriseSupplier;
import com.ruoyi.shop.result.QueryResult;
import com.ruoyi.shop.result.Result;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年11月17日?10:17
 */
public interface EnterpriseSupplierFeign {


    /***
     * EnterpriseSupplier分页条件搜索实现
     * @param enterpriseSupplier
     * @param page
     * @param size
     * @return
     */
    @PostMapping(value = "/enterprise/supplier/search/{page}/{size}" )
    Result<QueryResult<EnterpriseSupplier>> findPage(@RequestBody(required = false) EnterpriseSupplier enterpriseSupplier, @PathVariable("page") int page, @PathVariable("size") int size, @RequestParam(value = "fields", required = false) String fields);

    /***
     * 多条件搜索数据
     * @param enterpriseSupplier
     * @return
     */
    @PostMapping(value = "/enterprise/supplier/search" )
    Result<List<EnterpriseSupplier>> findList(@RequestBody(required = false) EnterpriseSupplier enterpriseSupplier, @RequestParam(value = "fields", required = false) String fields);


    /***
     * 根据ID查询EnterpriseSupplier数据
     * @param id
     * @return
     */
    @GetMapping("/enterprise/supplier/{id}")
    Result<EnterpriseSupplier> findById(@PathVariable("id") Long id);

    /**
     * 查询企业字段
     * @param id
     * @return
     */
    @GetMapping("/enterprise/supplier/field/{id}")
    Result<EnterpriseSupplier> findFieldById(@PathVariable("id") Long id, @RequestParam("fields") String fields);

    /***
     * 新增EnterpriseSupplier数据
     * @param enterpriseSupplier
     * @return
     */
    @PostMapping(value="/enterprise/supplier/add")
    Result<Boolean> add(@RequestBody EnterpriseSupplier enterpriseSupplier);

    /***
     * 修改Enterprise数据
     * @param enterpriseSupplier
     * @param id
     * @return
     */
    @PutMapping(value="/enterprise/supplier/{id}")
    Result<Boolean> update(@RequestBody EnterpriseSupplier enterpriseSupplier, @PathVariable("id") Long id);

    /***
     * 根据ID删除数据
     * @param id
     * @return
     */
    @DeleteMapping(value = "/enterprise/supplier/{id}" )
    Result<Boolean> delete(@PathVariable("id") Long id);

    /**
     * 根据ID查询信息
     * @param ids
     * @return
     */
    @GetMapping("/enterprise/supplier/ids")
    Result<List<EnterpriseSupplier>> findByIds(@RequestParam("ids") String ids, @RequestParam(value = "fields",required = false) String fields);

}
