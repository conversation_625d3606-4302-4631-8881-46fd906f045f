package com.ruoyi.shop.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.ruoyi.common.core.annotation.Excel;
//import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author:micah
 **/

@TableName("tb_product")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Product extends Model<Product>{

	@TableId(value = "id", type = IdType.AUTO)
	private Long id;//主键id

	private Long enterprise_id;//公司id

	private String system_no;//系统编号

	private String enterprise_name;//公司名称

	@Excel(name = "封面图片")
	private String cover;//封面图片

	private String product_no;//用户输入编号

	private Long classify_id;//一级分类ID

	private Long classify2_id;//二级分类ID

	private Long classify3_id;//三级分类ID

	@Excel(name = "产品名称")
	private String name;//产品名称

	@Excel(name = "产品类型")
	private String type;//活动类型  NORMAL 普通产品 GROUP 团购 CENTRAL 集采

	@Excel(name = "产品描述")
	private String description;//产品描述

	@Excel(name = "产品含税价")
	private BigDecimal tax_price;//产品含税价

	@Excel(name = "税率")
	private BigDecimal tax_rate;//税率

	private BigDecimal payment_rate;//集采定金比例

	@Excel(name = "运费")
	private BigDecimal freight;//运费

	@Excel(name = "产品单位")
	private String unit;//产品单位

	@Excel(name = "品牌")
	private String brand;//品牌

	@Excel(name = "库存")
	private BigDecimal stock;//库存

	@Excel(name = "型号")
	private String model;//型号

	@Excel(name = "最小起订量")
	private BigDecimal start_order;//最小起订量

	@Excel(name = "最小包装量")
	private BigDecimal start_pack;//最小包装量

	@Excel(name = "认证要求")
	private String identification;//认证要求 CCC,CB,VDE,CE,UL,CSA,TUV,GS,SAA,PSE,RoHS,REACH

	private BigDecimal sale_price;//销售价

	@Excel(name = "团购截止日期")
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Date deadline;//团购截止日期

	private String deliver_start;//集采交货开始时间

	private String deliver_end;//集采交货结束时间

	private Integer recommend;//推荐状态 0未推荐 1已推荐

	private String status;//产品状态 DRAFT-草稿 WAIT-待审核 ONLINE-已上线 REVOKE-已驳回 OFFLINE-已下线

	private BigDecimal sales;//销量

	private String central_status;//集采状态 GOING-进行中 STOP-已停止 DONE-已结束 CANCEL-已取消

	private String group_status;//团购状态 GOING-进行中 STOP-已停止 DONE-已结束 CANCEL-已取消

	private BigDecimal central_goal;//集采目标数量

	private BigDecimal central_real;//集采达成数量

	private BigDecimal central_percent;//集采比例

	private String central_rule;//集采规则

	private String central_note;//集采取消原因

	private String file_name;//集采附件名称

	private String file_path;//集采附件路径

	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String remark;//审批备注

	private Date create_time;//创建时间

	private String create_by;//创建人

	private Date update_time;//更新时间

	private String update_by;//更新人

	private Integer sorts;//排序

	private Integer isdel;//删除状态 0未删除 1已删除

	@Excel(name = "规格名称")
	private String normfile; //规格名称

	private String normurl;  //规格附件地址

	@TableField(exist = false)
	private String sortfield;

	@TableField(exist = false)
	private String sortorder;

	@TableField(exist = false)
	private String keyword;

	@Excel(name = "批次")
	private String batchno;  //批次

	@Excel(name = "一级分类")
	@TableField(exist = false)
	private String classify_name;//一级分类name

	@Excel(name = "二级分类")
	@TableField(exist = false)
	private String classify2_name;//二级分类name

	@Excel(name = "三级分类")
	@TableField(exist = false)
	private String classify3_name;//三级分类name

	@TableField(exist = false)
	private String source;

	@TableField(exist = false)
	private Integer classifyLevel; // 用户填写了几级分类，默认是0，遇到空就停

	private Long scene_id;//场景id

	private String scene_name;//场景名称
}
