package com.ruoyi.shop.feign.enterprise;

import com.ruoyi.shop.entity.Enterprise;
import com.ruoyi.shop.result.QueryResult;
import com.ruoyi.shop.result.Result;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022年05月19日?16:42
 */
public interface EnterpriseFeign {


    /***
     * Enterprise分页条件搜索实现
     * @param enterprise
     * @param page
     * @param size
     * @return
     */
    @PostMapping(value = "/enterprise/search/{page}/{size}" )
    Result<QueryResult<Enterprise>> findPage(@RequestBody(required = false) Enterprise enterprise, @PathVariable("page") int page, @PathVariable("size") int size, @RequestParam(value = "fields", required = false) String fields);

    /***
     * 多条件搜索数据
     * @param enterprise
     * @return
     */
    @PostMapping(value = "/enterprise/search" )
    Result<List<Enterprise>> findList(@RequestBody(required = false) Enterprise enterprise, @RequestParam(value = "fields", required = false) String fields);


    /***
     * 根据ID查询Enterprise数据
     * @param id
     * @return
     */
    @GetMapping("/enterprise/{id}")
    Result<Enterprise> findById(@PathVariable("id") Long id);

    /**
     * 查询企业字段
     * @param id
     * @return
     */
    @GetMapping("/enterprise/field/{id}")
    Result<Enterprise> findFieldById(@PathVariable("id") Long id, @RequestParam("fields") String fields);


    /***
     * 修改Enterprise数据
     * @param enterprise
     * @param id
     * @return
     */
    @PutMapping(value="/enterprise/{id}")
    Result<Boolean> update(@RequestBody Enterprise enterprise, @PathVariable("id") Long id);

    /***
     * 根据ID删除数据
     * @param id
     * @return
     */
    @DeleteMapping(value = "/enterprise/{id}" )
    Result<Boolean> delete(@PathVariable("id") Long id);

    /**
     * 根据ID查询信息
     * @param ids
     * @return
     */
    @GetMapping("/enterprise/ids")
    Result<List<Enterprise>> findByIds(@RequestParam("ids") String ids, @RequestParam(value = "fields",required = false) String fields);

    /***
     * 根据ID批量状态Enterprise数据
     * @param opid
     * @return
     */
    @PostMapping("/enterprise/state/op")
    Result<Boolean> stateOp(@RequestParam("opid") String opid,@RequestParam("status") String status);

    /***
     * 根据ID批量状态Enterprise数据
     * @param id
     * @return
     */
    @PostMapping("/enterprise/admin/op")
    Result<Boolean> adminOp(@RequestParam("id") Long id,@RequestParam("adminId") Long adminId);

    /**
     * 根据营业执照号码获取企业信息
     * @param businessNo
     * @param fields
     * @return
     */
    @GetMapping("/enterprise/no")
    Result<Enterprise> findFieldByBusinessNo(@RequestParam("businessNo") String businessNo, @RequestParam(value = "fields",required = false) String fields);
}
