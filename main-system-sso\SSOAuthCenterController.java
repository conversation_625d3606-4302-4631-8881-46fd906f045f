package com.ruoyi.auth.controller;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.auth.service.SSOAuthCenterService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.service.TokenService;
import com.ruoyi.system.api.model.LoginUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * SSO认证中心控制器
 * 主系统作为SSO认证中心，为其他系统提供认证服务
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/sso")
public class SSOAuthCenterController {

    private static final Logger log = LoggerFactory.getLogger(SSOAuthCenterController.class);

    @Autowired
    private SSOAuthCenterService ssoAuthCenterService;

    @Autowired
    private TokenService tokenService;

    /**
     * SSO登录接口
     * 用户在主系统登录成功后，生成SSO Token并跳转到目标系统
     * 
     * @param request 登录请求
     * @param response HTTP响应
     */
    @PostMapping("/login")
    public R<?> ssoLogin(@RequestBody JSONObject request, HttpServletResponse response) {
        try {
            String username = request.getString("username");
            String password = request.getString("password");
            String targetSystem = request.getString("targetSystem"); // 目标系统标识
            String redirectUrl = request.getString("redirectUrl"); // 登录成功后的重定向地址

            log.info("SSO登录请求 - 用户: {}, 目标系统: {}", username, targetSystem);

            // 1. 验证用户凭据
            LoginUser loginUser = ssoAuthCenterService.authenticate(username, password);
            if (loginUser == null) {
                return R.fail("用户名或密码错误");
            }

            // 2. 检查用户对目标系统的访问权限
            if (StringUtils.isNotEmpty(targetSystem)) {
                boolean hasPermission = ssoAuthCenterService.checkSystemPermission(loginUser, targetSystem);
                if (!hasPermission) {
                    return R.fail("用户无权限访问目标系统");
                }
            }

            // 3. 生成SSO Token
            String ssoToken = ssoAuthCenterService.generateSSOToken(loginUser, targetSystem);

            // 4. 生成主系统访问Token
            Map<String, Object> tokenMap = tokenService.createToken(loginUser);

            // 5. 记录SSO登录日志
            ssoAuthCenterService.recordSSOLogin(loginUser, targetSystem, ssoToken);

            // 6. 构造返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("ssoToken", ssoToken);
            result.put("accessToken", tokenMap.get("access_token"));
            result.put("expiresIn", tokenMap.get("expires_in"));
            result.put("user", loginUser);

            // 如果指定了目标系统，构造跳转URL
            if (StringUtils.isNotEmpty(targetSystem)) {
                String jumpUrl = ssoAuthCenterService.buildTargetSystemUrl(targetSystem, ssoToken, redirectUrl);
                result.put("jumpUrl", jumpUrl);
            }

            return R.ok(result);

        } catch (Exception e) {
            log.error("SSO登录失败", e);
            return R.fail("SSO登录失败: " + e.getMessage());
        }
    }

    /**
     * SSO Token验证接口
     * 其他系统调用此接口验证SSO Token的有效性
     */
    @PostMapping("/verify")
    public R<?> verifyToken(@RequestBody JSONObject request) {
        try {
            String ssoToken = request.getString("ssoToken");
            String targetSystem = request.getString("targetSystem");

            log.info("SSO Token验证请求 - Token: {}, 系统: {}", ssoToken, targetSystem);

            // 1. 验证Token有效性
            LoginUser loginUser = ssoAuthCenterService.validateSSOToken(ssoToken);
            if (loginUser == null) {
                return R.fail("SSO Token无效或已过期");
            }

            // 2. 检查用户对目标系统的权限
            boolean hasPermission = ssoAuthCenterService.checkSystemPermission(loginUser, targetSystem);
            if (!hasPermission) {
                return R.fail("用户无权限访问该系统");
            }

            // 3. 刷新Token过期时间
            ssoAuthCenterService.refreshSSOToken(ssoToken);

            // 4. 获取用户权限信息
            Map<String, Object> permissions = ssoAuthCenterService.getUserPermissions(loginUser, targetSystem);

            // 5. 构造返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("valid", true);
            result.put("userId", loginUser.getUserId());
            result.put("username", loginUser.getUsername());
            result.put("nickname", loginUser.getUser().getNickName());
            result.put("phone", loginUser.getUser().getPhonenumber());
            result.put("email", loginUser.getUser().getEmail());
            result.put("permissions", permissions);

            return R.ok(result);

        } catch (Exception e) {
            log.error("SSO Token验证失败", e);
            return R.fail("Token验证失败: " + e.getMessage());
        }
    }

    /**
     * SSO登出接口
     * 用户登出时调用，通知所有关联系统
     */
    @PostMapping("/logout")
    public R<?> ssoLogout(@RequestBody JSONObject request) {
        try {
            String ssoToken = request.getString("ssoToken");
            String userId = request.getString("userId");

            log.info("SSO登出请求 - Token: {}, 用户ID: {}", ssoToken, userId);

            // 1. 验证Token有效性
            LoginUser loginUser = ssoAuthCenterService.validateSSOToken(ssoToken);
            if (loginUser == null) {
                log.warn("登出时Token已无效: {}", ssoToken);
            }

            // 2. 清除SSO Token缓存
            ssoAuthCenterService.clearSSOToken(ssoToken);

            // 3. 通知所有关联系统用户已登出
            ssoAuthCenterService.notifyAllSystemsLogout(userId, ssoToken);

            // 4. 记录登出日志
            ssoAuthCenterService.recordSSOLogout(userId, ssoToken);

            return R.ok("SSO登出成功");

        } catch (Exception e) {
            log.error("SSO登出失败", e);
            return R.fail("SSO登出失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户信息接口
     * 其他系统可调用此接口获取用户详细信息
     */
    @PostMapping("/userinfo")
    public R<?> getUserInfo(@RequestBody JSONObject request) {
        try {
            String ssoToken = request.getString("ssoToken");
            String userId = request.getString("userId");

            // 优先使用Token验证
            LoginUser loginUser = null;
            if (StringUtils.isNotEmpty(ssoToken)) {
                loginUser = ssoAuthCenterService.validateSSOToken(ssoToken);
            } else if (StringUtils.isNotEmpty(userId)) {
                loginUser = ssoAuthCenterService.getUserById(Long.valueOf(userId));
            }

            if (loginUser == null) {
                return R.fail("用户信息不存在");
            }

            // 构造用户信息
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("userId", loginUser.getUserId());
            userInfo.put("username", loginUser.getUsername());
            userInfo.put("nickname", loginUser.getUser().getNickName());
            userInfo.put("phone", loginUser.getUser().getPhonenumber());
            userInfo.put("email", loginUser.getUser().getEmail());
            userInfo.put("status", loginUser.getUser().getStatus());
            userInfo.put("deptId", loginUser.getUser().getDeptId());
            userInfo.put("roles", loginUser.getUser().getRoles());

            return R.ok(userInfo);

        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            return R.fail("获取用户信息失败: " + e.getMessage());
        }
    }

    /**
     * 系统注册接口
     * 新系统接入SSO时调用此接口注册
     */
    @PostMapping("/register")
    public R<?> registerSystem(@RequestBody JSONObject request) {
        try {
            String systemId = request.getString("systemId");
            String systemName = request.getString("systemName");
            String callbackUrl = request.getString("callbackUrl");
            String logoutUrl = request.getString("logoutUrl");

            log.info("系统注册请求 - ID: {}, 名称: {}", systemId, systemName);

            boolean success = ssoAuthCenterService.registerSystem(systemId, systemName, callbackUrl, logoutUrl);

            if (success) {
                return R.ok("系统注册成功");
            } else {
                return R.fail("系统注册失败");
            }

        } catch (Exception e) {
            log.error("系统注册失败", e);
            return R.fail("系统注册失败: " + e.getMessage());
        }
    }

    /**
     * 跳转到目标系统
     * 前端页面跳转使用
     */
    @GetMapping("/jump")
    public void jumpToTargetSystem(@RequestParam String targetSystem,
                                   @RequestParam String ssoToken,
                                   @RequestParam(required = false) String redirectUrl,
                                   HttpServletResponse response) throws IOException {
        try {
            String jumpUrl = ssoAuthCenterService.buildTargetSystemUrl(targetSystem, ssoToken, redirectUrl);
            response.sendRedirect(jumpUrl);
        } catch (Exception e) {
            log.error("跳转到目标系统失败", e);
            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "跳转失败");
        }
    }
}
