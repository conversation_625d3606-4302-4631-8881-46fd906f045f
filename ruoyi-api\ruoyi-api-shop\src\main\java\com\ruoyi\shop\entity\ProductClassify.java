package com.ruoyi.shop.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * @Author:micah
 **/

@TableName("tb_product_classify")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductClassify extends Model<ProductClassify>{

	@TableId(value = "id", type = IdType.AUTO)
	private Long id;//主键id

	private Integer sorts;//排序数

	private Long pid;//上级ID

	private String name;//分类名称

	private String charger;//品类负责人

	private String manager;//采购经理

	private String director;//采购总监

	private String leader;//品类分管领导

	private String remark;//备注

	private Integer recommend;//推荐状态

	private Integer status;//状态  1上线中 0已下线

	private Integer isdel;//删除状态 0未删除 1已删除

	private String create_by;//创建者

	private Date create_time;//创建时间

	private String update_by;//更新者

	private Date update_time;//更新时间

	/**
	 * 一级分类logo
	 * */
	private String logo;

	@TableField(exist = false)
	private String orderfield;

	@TableField(exist = false)
	private List<ProductClassify> children; // 子分类

	@TableField(exist = false)
	private Integer level; //层级 1 2 3

	private Integer map_rec;//场景推荐状态  1不推荐 2推荐

	private String scene_pic;//场景图

}
