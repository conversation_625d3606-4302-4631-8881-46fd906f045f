package com.ruoyi.shop.enums.product;

/**
 * @program: ruoyi
 * @description:
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2022-05-18 09:50
 **/
public enum CentralStatus {

    GOING("GOING","集采中"),
    STOP("STOP","已停止"),
    DONE("DONE","已结束"),
    CANCEL("CANCEL","已取消");

    private final String key;
    private final String value;

    CentralStatus(String key, String value){
        this.key = key;
        this.value = value;
    }
    //根据key获取枚举
    public static CentralStatus getCentralStatus(String key){
        if(null == key){
            return null;
        }
        for(CentralStatus temp: CentralStatus.values()){
            if(temp.getKey().equals(key)){
                return temp;
            }
        }
        return null;
    }

    public String getKey() {
        return key;
    }
    public String getValue() {
        return value;
    }
}
