package com.ruoyi.shop.feign.order;

import com.ruoyi.shop.entity.Offer;
import com.ruoyi.shop.entity.OrderItems;
import com.ruoyi.shop.result.QueryResult; import com.ruoyi.shop.result.Result;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * @Author:micah
 **/

public interface OrderItemsFeign {

    /***
     * OrderItems分页条件搜索实现
     * @param orderItems
     * @param page
     * @param size
     * @return
     */
    @PostMapping(value = "/order/items/search/{page}/{size}" )
    Result<QueryResult<OrderItems>> findPage(@RequestBody(required = false) OrderItems orderItems, @PathVariable("page") int page, @PathVariable("size") int size, @RequestParam(value = "fields", required = false) String fields);

    /***
     * 多条件搜索数据
     * @param orderItems
     * @return
     */
    @PostMapping(value = "/order/items/search" )
    Result<List<OrderItems>> findList(@RequestBody(required = false) OrderItems orderItems, @RequestParam(value = "fields", required = false) String fields);

    /***
     * 根据ID删除数据
     * @param id
     * @return
     */
    @DeleteMapping(value = "/order/items/{id}" )
    Result<Boolean> delete(@PathVariable("id") Long id);

    /***
     * 修改OrderItems数据
     * @param orderItems
     * @param id
     * @return
     */
    @PutMapping(value="/order/items/{id}")
    Result<Boolean> update(@RequestBody OrderItems orderItems, @PathVariable("id") Long id);

    /***
     * 批量状态OrderItems数据
     * @param opid
     * @return
     */
    @PostMapping("/order/items/state/op")
    Result<Boolean> stateOp(@RequestParam("opid") String opid, @RequestParam("status") Integer status);

    /***
     * 根据ID查询OrderItems数据
     * @param id
     * @return
     */
    @GetMapping("/order/items/{id}")
    Result<OrderItems> findById(@PathVariable("id") Long id);


    /**
     * 根据ID查询信息
     *
     * @param ids
     * @return
     */
    @GetMapping("/order/items/ids")
    Result<List<OrderItems>> findByIds(@RequestParam("ids") String ids, @RequestParam(value = "fields", required = false) String fields);



    /**
     * 根据orderId查询信息
     *
     * @param orderIds
     * @return
     */
    @GetMapping("/order/items/orderIds")
    Result<List<OrderItems>> findByOrderIds(@RequestParam("orderIds") String orderIds, @RequestParam(value = "fields", required = false) String fields);

}