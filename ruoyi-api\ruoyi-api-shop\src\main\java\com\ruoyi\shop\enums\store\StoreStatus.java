package com.ruoyi.shop.enums.store;

/**
 * 店铺状态
 */
public enum StoreStatus {

    WAIT("WAIT","待审核"),
    OPEN("OPEN","已开通"),
    DENY("DENY","已驳回"),
    CLOSE("CLOSE","已关店");

    private final String key;
    private final String value;

    StoreStatus(String key, String value){
        this.key = key;
        this.value = value;
    }
    //根据key获取枚举
    public static StoreStatus getStoreStatus(String key){
        if(null == key){
            return null;
        }
        for(StoreStatus temp: StoreStatus.values()){
            if(temp.getKey().equals(key)){
                return temp;
            }
        }
        return null;
    }

    public String getKey() {
        return key;
    }
    public String getValue() {
        return value;
    }
}
