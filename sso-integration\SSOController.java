package com.ruoyi.auth.controller;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.auth.service.SSOService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.service.TokenService;
import com.ruoyi.system.api.model.LoginUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * SSO单点登录控制器
 * 用于与主系统(share-intelligent-backend)的SSO集成
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/sso")
public class SSOController {

    private static final Logger log = LoggerFactory.getLogger(SSOController.class);

    @Autowired
    private SSOService ssoService;

    @Autowired
    private TokenService tokenService;

    /**
     * SSO登录入口
     * 主系统跳转到此接口进行SSO登录
     * 
     * @param ssoToken 主系统颁发的SSO Token
     * @param redirectUrl 登录成功后的重定向地址
     */
    @GetMapping("/login")
    public R<?> ssoLogin(@RequestParam("token") String ssoToken,
                         @RequestParam(value = "redirect", required = false) String redirectUrl,
                         HttpServletResponse response) {
        try {
            log.info("SSO登录请求，token: {}, redirect: {}", ssoToken, redirectUrl);

            // 1. 验证SSO Token有效性
            JSONObject userInfo = ssoService.validateSSOToken(ssoToken);
            if (userInfo == null) {
                return R.fail("SSO Token无效或已过期");
            }

            // 2. 根据主系统用户信息获取或创建本地用户
            LoginUser loginUser = ssoService.getOrCreateLocalUser(userInfo);
            if (loginUser == null) {
                return R.fail("用户信息同步失败");
            }

            // 3. 生成本地系统的访问Token
            Map<String, Object> tokenMap = tokenService.createToken(loginUser);

            // 4. 记录SSO登录日志
            ssoService.recordSSOLogin(loginUser, ssoToken);

            // 5. 构造返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("code", 200);
            result.put("msg", "SSO登录成功");
            result.put("token", tokenMap.get("access_token"));
            result.put("expires_in", tokenMap.get("expires_in"));
            result.put("user", loginUser);
            
            if (StringUtils.isNotEmpty(redirectUrl)) {
                result.put("redirect", redirectUrl);
            }

            return R.ok(result);

        } catch (Exception e) {
            log.error("SSO登录失败", e);
            return R.fail("SSO登录失败: " + e.getMessage());
        }
    }

    /**
     * SSO登出接口
     * 主系统通知本系统用户已登出
     */
    @PostMapping("/logout")
    public R<?> ssoLogout(@RequestBody JSONObject request) {
        try {
            String ssoToken = request.getString("ssoToken");
            String userId = request.getString("userId");

            log.info("收到SSO登出通知，ssoToken: {}, userId: {}", ssoToken, userId);

            // 1. 清除本地用户会话
            boolean success = ssoService.clearLocalUserSession(userId, ssoToken);

            if (success) {
                return R.ok("SSO登出成功");
            } else {
                return R.fail("SSO登出失败");
            }

        } catch (Exception e) {
            log.error("SSO登出失败", e);
            return R.fail("SSO登出失败: " + e.getMessage());
        }
    }

    /**
     * 检查SSO状态
     * 用于前端检查当前用户的SSO状态
     */
    @GetMapping("/status")
    public R<?> checkSSOStatus(HttpServletRequest request) {
        try {
            // 获取当前用户Token
            String token = request.getHeader("Authorization");
            if (StringUtils.isEmpty(token)) {
                return R.fail("未登录");
            }

            // 获取用户信息
            LoginUser loginUser = tokenService.getLoginUser(token);
            if (loginUser == null) {
                return R.fail("用户信息无效");
            }

            // 检查SSO状态
            boolean ssoValid = ssoService.checkSSOStatus(loginUser);

            Map<String, Object> result = new HashMap<>();
            result.put("ssoValid", ssoValid);
            result.put("userId", loginUser.getUserId());
            result.put("username", loginUser.getUsername());

            return R.ok(result);

        } catch (Exception e) {
            log.error("检查SSO状态失败", e);
            return R.fail("检查SSO状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取主系统登录地址
     * 前端可调用此接口获取主系统的登录地址
     */
    @GetMapping("/loginUrl")
    public R<?> getMainSystemLoginUrl(@RequestParam(value = "redirect", required = false) String redirectUrl) {
        try {
            String loginUrl = ssoService.getMainSystemLoginUrl(redirectUrl);
            
            Map<String, Object> result = new HashMap<>();
            result.put("loginUrl", loginUrl);
            
            return R.ok(result);

        } catch (Exception e) {
            log.error("获取主系统登录地址失败", e);
            return R.fail("获取主系统登录地址失败: " + e.getMessage());
        }
    }

    /**
     * 用户信息同步接口
     * 主系统用户信息变更时调用此接口同步
     */
    @PostMapping("/syncUser")
    public R<?> syncUserInfo(@RequestBody JSONObject userInfo) {
        try {
            log.info("收到用户信息同步请求: {}", userInfo);

            boolean success = ssoService.syncUserInfo(userInfo);

            if (success) {
                return R.ok("用户信息同步成功");
            } else {
                return R.fail("用户信息同步失败");
            }

        } catch (Exception e) {
            log.error("用户信息同步失败", e);
            return R.fail("用户信息同步失败: " + e.getMessage());
        }
    }
}
