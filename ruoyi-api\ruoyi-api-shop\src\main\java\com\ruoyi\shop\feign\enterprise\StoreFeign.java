package com.ruoyi.shop.feign.enterprise;

import com.ruoyi.shop.entity.Store;
import com.ruoyi.shop.result.QueryResult; import com.ruoyi.shop.result.Result;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * @Author:micah
 **/
public interface StoreFeign {

    /***
     * Store分页条件搜索实现
     * @param store
     * @param page
     * @param size
     * @return
     */
    @PostMapping(value = "/store/search/{page}/{size}" )
    Result<QueryResult<Store>> findPage(@RequestBody(required = false) Store store, @PathVariable("page") int page, @PathVariable("size") int size, @RequestParam(value = "fields", required = false) String fields);

    /***
     * 多条件搜索数据
     * @param store
     * @return
     */
    @PostMapping(value = "/store/search" )
    Result<List<Store>> findList(@RequestBody(required = false) Store store, @RequestParam(value = "fields", required = false) String fields);

    /***
     * 修改Store数据
     * @param store
     * @param id
     * @return
     */
    @PutMapping(value="/store/{id}")
    Result<Boolean> update(@RequestBody Store store, @PathVariable("id") Long id);

    /***
     * 新增Store数据
     * @param store
     * @return
     */
    @PostMapping(value="/store")
    Result<Boolean> add(@RequestBody Store store);

    /***
     * 批量状态Store数据
     * @param opid
     * @return
     */
    @PostMapping("/store/state/op")
    Result<Boolean> stateOp(@RequestParam("opid") String opid, @RequestParam("status") String status);

    /***
     * 根据ID查询Store数据
     * @param id
     * @return
     */
    @GetMapping("/store/{id}")
    Result<Store> findById(@PathVariable("id") Long id);
}