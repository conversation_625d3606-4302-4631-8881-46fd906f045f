package com.ruoyi.shop.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * @Author:micah
 **/

@TableName("tb_user")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class User extends Model<User>{

	@TableId(value = "id", type = IdType.AUTO)
	private Long id;//用户ID

	private String openid;

	private Long enterprise_id;//公司ID

	@TableField(updateStrategy = FieldStrategy.IGNORED)
	@Excel(name = "企业名称")
	private String enterprise_name;//公司名称

	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String business_no;//营业执照号码

	@Excel(name = "用户类型", readConverterExp = "ADMIN=管理员,STAFF=员工")
	private String type;//用户类型

	@Excel(name = "手机号")
	private String telphone;//手机号码

	private String password;//密码

	private String salt;//秘钥

	@Excel(name = "状态", readConverterExp = "1=正常,0=停用")
	private Integer status;//帐号状态（1正常 0停用）

	private String customer;//客服类型

	@Excel(name = "最后登陆时间",dateFormat = "yyyy-MM-dd HH:mm:ss")
	private Date login_date;//最后登陆时间

	private String create_by;//创建者

	private Date create_time;//创建时间

	private String update_by;//更新者

	private Date update_time;//更新时间

	@TableField(exist = false)
	private List<Long> ids;

	@TableField(exist = false)
	private String plainpwd;

}
