package com.ruoyi.shop.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:micah
 **/

@TableName("tb_infor_info")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InforInfo extends Model<InforInfo>{

	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	private Long infor_id;//文章ID

	private String content;//详情


}
