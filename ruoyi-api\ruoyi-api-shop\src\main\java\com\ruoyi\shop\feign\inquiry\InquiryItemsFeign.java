package com.ruoyi.shop.feign.inquiry;

import com.ruoyi.shop.entity.InquiryItems;
import com.ruoyi.shop.result.QueryResult; import com.ruoyi.shop.result.Result;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * @Author:micah
 **/

public interface InquiryItemsFeign {

    /***
     * InquiryItems分页条件搜索实现
     * @param inquiryItems
     * @param page
     * @param size
     * @return
     */
    @PostMapping(value = "/inquiry/items/search/{page}/{size}" )
    Result<QueryResult<InquiryItems>> findPage(@RequestBody(required = false) InquiryItems inquiryItems, @PathVariable("page") int page, @PathVariable("size") int size, @RequestParam(value = "fields", required = false) String fields);

    /***
     * 多条件搜索数据
     * @param inquiryItems
     * @return
     */
    @PostMapping(value = "/inquiry/items/search" )
    Result<List<InquiryItems>> findList(@RequestBody(required = false) InquiryItems inquiryItems, @RequestParam(value = "fields", required = false) String fields);

    /***
     * 根据ID删除数据
     * @param id
     * @return
     */
    @DeleteMapping(value = "/inquiry/items/{id}" )
    Result<Boolean> delete(@PathVariable("id") Long id);

    /***
     * 修改InquiryItems数据
     * @param inquiryItems
     * @param id
     * @return
     */
    @PutMapping(value="/inquiry/items/{id}")
    Result<Boolean> update(@RequestBody InquiryItems inquiryItems, @PathVariable("id") Long id);

    /***
     * 批量刪除InquiryItems数据
     * @param opid
     * @return
     */
    @PostMapping("/inquiry/items/batch/delete")
    Result<Boolean> batchDelete(@RequestParam("opid") String opid);

    /***
     * 批量状态InquiryItems数据
     * @param opid
     * @return
     */
    @PostMapping("/inquiry/items/state/op")
    Result<Boolean> stateOp(@RequestParam("opid") String opid, @RequestParam("status") Integer status);

    /***
     * 根据ID查询InquiryItems数据
     * @param id
     * @return
     */
    @GetMapping("/inquiry/items/{id}")
    Result<InquiryItems> findById(@PathVariable("id") Long id);
}