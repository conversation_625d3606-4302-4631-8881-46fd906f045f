# SSO单点登录配置
sso:
  # 主系统配置
  main-system:
    # 主系统地址
    url: http://localhost:8080
    # Token验证接口
    verify-token-url: /api/sso/verify
    # 登录页面地址
    login-url: /login
    # 登出通知接口
    logout-url: /api/sso/logout
    # 用户同步接口
    sync-user-url: /api/sso/syncUser
  
  # 当前系统配置
  current-system:
    # 系统标识
    system-id: market
    # 系统名称
    system-name: 智能市场系统
    # SSO回调地址
    callback-url: http://localhost:8080/sso/login
  
  # Token配置
  token:
    # Token过期时间（秒）
    expire-time: 7200
    # Token刷新时间（秒）
    refresh-time: 1800
  
  # 缓存配置
  cache:
    # 缓存前缀
    prefix: sso:market:
    # 缓存过期时间（秒）
    expire-time: 7200

# RestTemplate配置
rest-template:
  # 连接超时时间（毫秒）
  connect-timeout: 5000
  # 读取超时时间（毫秒）
  read-timeout: 10000
