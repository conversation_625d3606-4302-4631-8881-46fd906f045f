package com.ruoyi.shop.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Author:micah
 **/

@TableName("tb_entrance")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Entrance extends Model<Entrance>{

	@TableId(value = "id", type = IdType.AUTO)
	private Long id;//主键id

	private String name;//名称

	private String url;//入口地址

	private String icon;//图标

	//private Integer sorts;//排序

	private Integer status;//状态  1正常 0停用

	private String create_by;//创建者

	private Date create_time;//创建时间

	private String update_by;//更新者

	private Date update_time;//更新时间

	private Long enterprise_id;//企业id

	private String enterprise_name;//企业名称


}
