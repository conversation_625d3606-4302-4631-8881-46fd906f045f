package com.ruoyi.shop.feign.order;

import com.ruoyi.shop.entity.OrderDeliver;
import com.ruoyi.shop.result.QueryResult;
import com.ruoyi.shop.result.Result;
import com.ruoyi.shop.vo.OrderDeliverVO;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * @Author:micah
 **/

public interface OrderDeliverFeign {

    /***
     * OrderDeliver分页条件搜索实现
     * @param orderDeliver
     * @param page
     * @param size
     * @return
     */
    @PostMapping(value = "/order/deliver/search/{page}/{size}")
    Result<QueryResult<OrderDeliver>> findPage(@RequestBody(required = false) OrderDeliver orderDeliver, @PathVariable("page") int page, @PathVariable("size") int size, @RequestParam(value = "fields", required = false) String fields);

    @PostMapping(value = "/order/deliver/searchPage")
    Result<QueryResult<Map<String, Object>>> searchPage(@RequestBody(required = false) OrderDeliverVO deliverVO);
    /***
     * 多条件搜索数据
     * @param orderDeliver
     * @return
     */
    @PostMapping(value = "/order/deliver/search")
    Result<List<OrderDeliver>> findList(@RequestBody(required = false) OrderDeliver orderDeliver, @RequestParam(value = "fields", required = false) String fields);

    /***
     * 修改OrderDeliver数据
     * @param orderDelivers
     * @param
     * @return
     */
    @PutMapping(value = "/order/deliver/update")
    Result<Boolean> update(@RequestBody List<OrderDeliver> orderDelivers);

    /***
     * 新增OrderDeliver数据
     * @param orderDeliver
     * @return
     */
    @PostMapping(value = "/order/deliver")
    Result<Boolean> add(@RequestBody OrderDeliver orderDeliver);
    /***
     * 新增OrderDeliver数据
     * @param orderDelivers
     * @return
     */
    @PostMapping(value = "/order/deliver/batch/add")
    Result<Boolean> batchAdd(@RequestBody List<OrderDeliver> orderDelivers);


    /***
     * 根据ID查询OrderDeliver数据
     * @param id
     * @return
     */
    @GetMapping("/order/deliver/{id}")
    Result<OrderDeliver> findById(@PathVariable("id") Long id);

    @PostMapping("/order/deliver/updateAfterStatus")
    Result<Boolean> updateAfterStatus(@RequestParam("opid") String opid);
}