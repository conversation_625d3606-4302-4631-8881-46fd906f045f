# 主系统SSO认证中心实现方案

## 概述

本方案将主系统（`share-intelligent-backend`）改造为SSO认证中心，为其他系统提供统一的身份认证服务。

## 架构设计

```
主系统 SSO认证中心
├── 用户认证服务
├── SSO Token生成与验证
├── 系统注册管理
├── 权限控制
└── 登出通知服务
```

## 核心功能

### 1. 认证中心服务

- **用户登录认证** - 验证用户身份
- **SSO Token生成** - 为认证用户生成跨系统Token
- **Token验证服务** - 为其他系统提供Token验证
- **权限管理** - 控制用户对不同系统的访问权限

### 2. 系统管理

- **系统注册** - 管理接入的子系统
- **跳转管理** - 处理系统间跳转
- **登出通知** - 统一登出时通知所有系统

## 文件结构

### 后端文件

```
main-system-sso/
├── SSOAuthCenterController.java    # SSO认证中心API控制器
├── SSOLoginController.java         # SSO登录页面控制器
├── SSOAuthCenterService.java       # SSO认证中心服务实现
├── application-sso-center.yml      # SSO认证中心配置
└── README-MainSystem.md           # 本文档
```

### 前端页面（需要创建）

```
templates/sso/
├── login.html          # SSO统一登录页面
├── selectSystem.html   # 系统选择页面
├── logout.html         # 登出页面
└── error.html          # 错误页面
```

## API接口说明

### 1. 认证中心API (`/api/sso`)

#### POST `/api/sso/login`
SSO登录接口
```json
{
  "username": "用户名",
  "password": "密码",
  "targetSystem": "目标系统ID（可选）",
  "redirectUrl": "登录成功后重定向地址（可选）"
}
```

#### POST `/api/sso/verify`
Token验证接口（供其他系统调用）
```json
{
  "ssoToken": "SSO Token",
  "targetSystem": "请求验证的系统ID"
}
```

#### POST `/api/sso/logout`
SSO登出接口
```json
{
  "ssoToken": "SSO Token",
  "userId": "用户ID"
}
```

#### POST `/api/sso/userinfo`
获取用户信息接口
```json
{
  "ssoToken": "SSO Token",
  "userId": "用户ID（可选）"
}
```

### 2. 登录页面API (`/sso`)

#### GET `/sso/login`
SSO登录页面
- 参数：`target`（目标系统）, `redirect`（重定向地址）

#### POST `/sso/doLogin`
处理登录表单提交

#### GET `/sso/jumpTo/{systemId}`
跳转到指定系统

## 配置说明

### 1. 系统配置

在 `application-sso-center.yml` 中配置：

```yaml
sso:
  token:
    expire-time: 7200  # Token过期时间（秒）
    secret: ruoyi-sso-secret-key-2024  # Token密钥
  
  registered-systems:
    market:  # 系统ID
      system-id: market
      system-name: 智能市场系统
      callback-url: http://localhost:8081/sso/login
      logout-url: http://localhost:8081/sso/logout
      status: active
```

### 2. Redis配置

确保Redis服务正常运行，用于缓存SSO Token和用户会话。

## 部署步骤

### 1. 后端部署

1. **添加文件到主系统项目**
   ```
   src/main/java/com/ruoyi/auth/controller/
   ├── SSOAuthCenterController.java
   └── SSOLoginController.java
   
   src/main/java/com/ruoyi/auth/service/
   └── SSOAuthCenterService.java
   ```

2. **更新配置文件**
   - 将 `application-sso-center.yml` 内容合并到主系统配置文件

3. **添加依赖**（如果需要）
   ```xml
   <dependency>
       <groupId>org.springframework.boot</groupId>
       <artifactId>spring-boot-starter-web</artifactId>
   </dependency>
   <dependency>
       <groupId>org.springframework.boot</groupId>
       <artifactId>spring-boot-starter-thymeleaf</artifactId>
   </dependency>
   ```

### 2. 前端页面部署

1. **创建登录页面模板**
   - 在 `src/main/resources/templates/sso/` 目录下创建页面模板

2. **集成现有登录逻辑**
   - 将现有登录页面改造为支持SSO的统一登录页面

### 3. 数据库配置

如果需要持久化系统注册信息，可以创建相关表：

```sql
-- SSO系统注册表
CREATE TABLE sso_registered_systems (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    system_id VARCHAR(50) UNIQUE NOT NULL,
    system_name VARCHAR(100) NOT NULL,
    callback_url VARCHAR(200) NOT NULL,
    logout_url VARCHAR(200),
    status VARCHAR(20) DEFAULT 'active',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- SSO登录日志表
CREATE TABLE sso_login_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    username VARCHAR(50) NOT NULL,
    target_system VARCHAR(50),
    sso_token VARCHAR(500),
    login_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(50),
    user_agent TEXT
);
```

## 使用流程

### 1. 用户登录流程

1. **访问登录页面**
   ```
   http://主系统地址/sso/login?target=market&redirect=http://目标地址
   ```

2. **用户输入凭据并登录**

3. **系统验证用户身份**

4. **生成SSO Token**

5. **跳转到目标系统**
   ```
   http://目标系统地址/sso/login?token=SSO_TOKEN&redirect=目标地址
   ```

### 2. 其他系统验证流程

1. **接收SSO Token**

2. **调用主系统验证接口**
   ```
   POST http://主系统地址/api/sso/verify
   {
     "ssoToken": "token",
     "targetSystem": "system_id"
   }
   ```

3. **获取用户信息并创建本地会话**

### 3. 登出流程

1. **用户在任一系统登出**

2. **调用主系统登出接口**

3. **主系统通知所有关联系统**

4. **清除所有系统的用户会话**

## 安全考虑

### 1. Token安全

- **有效期控制** - Token默认2小时过期
- **签名验证** - 使用JWT签名防止篡改
- **HTTPS传输** - 生产环境必须使用HTTPS

### 2. 权限控制

- **系统访问权限** - 控制用户对不同系统的访问
- **角色权限映射** - 根据用户角色分配系统权限

### 3. 会话管理

- **Redis缓存** - 使用Redis管理会话状态
- **定期清理** - 自动清理过期Token和会话

## 测试方案

### 1. 单元测试

- 测试用户认证逻辑
- 测试Token生成和验证
- 测试权限检查逻辑

### 2. 集成测试

- 测试完整的SSO登录流程
- 测试系统间跳转
- 测试统一登出功能

### 3. 性能测试

- 测试并发登录性能
- 测试Token验证性能
- 测试Redis缓存性能

## 监控和日志

### 1. 关键指标监控

- SSO登录成功率
- Token验证响应时间
- 系统间跳转成功率

### 2. 日志记录

- 用户登录日志
- Token验证日志
- 系统跳转日志
- 错误日志

## 扩展功能

### 1. 高级认证

- 多因子认证（MFA）
- 记住我功能
- 社交登录集成

### 2. 管理功能

- SSO管理后台
- 系统注册管理
- 用户权限管理
- 登录统计分析

## 故障排查

### 1. 常见问题

- **Token验证失败** - 检查Token格式和过期时间
- **系统跳转失败** - 检查系统注册配置
- **权限验证失败** - 检查用户权限配置

### 2. 日志查看

查看相关日志：
```bash
# SSO认证日志
tail -f logs/sso-auth.log

# 系统跳转日志
tail -f logs/sso-jump.log
```

这个方案提供了完整的主系统SSO认证中心实现，支持多系统接入和统一身份管理。
