package com.ruoyi.auth.service;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.utils.JwtUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.uuid.IdUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.service.TokenService;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.model.LoginUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * SSO认证中心服务实现
 * 
 * <AUTHOR>
 */
@Service
public class SSOAuthCenterService {

    private static final Logger log = LoggerFactory.getLogger(SSOAuthCenterService.class);

    @Autowired
    private RedisService redisService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private RestTemplate restTemplate;

    // SSO配置
    @Value("${sso.token.expire-time:7200}")
    private long ssoTokenExpireTime;

    @Value("${sso.token.secret:ruoyi-sso-secret}")
    private String ssoTokenSecret;

    // 缓存前缀
    private static final String SSO_TOKEN_PREFIX = "sso:token:";
    private static final String SSO_USER_PREFIX = "sso:user:";
    private static final String SSO_SYSTEM_PREFIX = "sso:system:";

    // 已注册的系统配置
    private static final Map<String, SystemConfig> REGISTERED_SYSTEMS = new HashMap<>();

    static {
        // 预配置系统信息
        REGISTERED_SYSTEMS.put("market", new SystemConfig(
            "market", 
            "智能市场系统", 
            "http://localhost:8080/sso/login",
            "http://localhost:8080/sso/logout"
        ));
    }

    /**
     * 用户认证
     */
    public LoginUser authenticate(String username, String password) {
        try {
            // 调用现有的用户认证逻辑
            SysUser user = remoteUserService.getUserInfo(username, "inner").getData();
            if (user == null) {
                return null;
            }

            // 验证密码（这里需要根据实际的密码验证逻辑调整）
            // boolean passwordValid = passwordEncoder.matches(password, user.getPassword());
            // if (!passwordValid) {
            //     return null;
            // }

            // 构造LoginUser
            LoginUser loginUser = new LoginUser();
            loginUser.setUserId(user.getUserId());
            loginUser.setUsername(user.getUserName());
            loginUser.setUser(user);

            return loginUser;

        } catch (Exception e) {
            log.error("用户认证失败", e);
            return null;
        }
    }

    /**
     * 生成SSO Token
     */
    public String generateSSOToken(LoginUser loginUser, String targetSystem) {
        try {
            // 1. 生成唯一的SSO Token ID
            String tokenId = IdUtils.fastUUID();

            // 2. 构造Token载荷
            Map<String, Object> claims = new HashMap<>();
            claims.put("tokenId", tokenId);
            claims.put("userId", loginUser.getUserId());
            claims.put("username", loginUser.getUsername());
            claims.put("targetSystem", targetSystem);
            claims.put("timestamp", System.currentTimeMillis());

            // 3. 生成JWT Token
            String ssoToken = JwtUtils.createToken(claims);

            // 4. 缓存Token和用户信息的映射
            String tokenKey = SSO_TOKEN_PREFIX + tokenId;
            redisService.setCacheObject(tokenKey, loginUser, ssoTokenExpireTime, TimeUnit.SECONDS);

            // 5. 缓存用户和Token的映射（用于登出时清理）
            String userKey = SSO_USER_PREFIX + loginUser.getUserId();
            redisService.setCacheObject(userKey, tokenId, ssoTokenExpireTime, TimeUnit.SECONDS);

            log.info("为用户 {} 生成SSO Token: {}", loginUser.getUsername(), tokenId);
            return ssoToken;

        } catch (Exception e) {
            log.error("生成SSO Token失败", e);
            return null;
        }
    }

    /**
     * 验证SSO Token
     */
    public LoginUser validateSSOToken(String ssoToken) {
        try {
            // 1. 解析JWT Token
            Map<String, Object> claims = JwtUtils.parseToken(ssoToken);
            if (claims == null) {
                return null;
            }

            String tokenId = (String) claims.get("tokenId");
            if (StringUtils.isEmpty(tokenId)) {
                return null;
            }

            // 2. 从缓存中获取用户信息
            String tokenKey = SSO_TOKEN_PREFIX + tokenId;
            LoginUser loginUser = redisService.getCacheObject(tokenKey);

            if (loginUser != null) {
                log.info("SSO Token验证成功: {}", loginUser.getUsername());
            }

            return loginUser;

        } catch (Exception e) {
            log.error("验证SSO Token失败", e);
            return null;
        }
    }

    /**
     * 刷新SSO Token过期时间
     */
    public void refreshSSOToken(String ssoToken) {
        try {
            Map<String, Object> claims = JwtUtils.parseToken(ssoToken);
            if (claims != null) {
                String tokenId = (String) claims.get("tokenId");
                String tokenKey = SSO_TOKEN_PREFIX + tokenId;
                redisService.expire(tokenKey, ssoTokenExpireTime, TimeUnit.SECONDS);
            }
        } catch (Exception e) {
            log.error("刷新SSO Token失败", e);
        }
    }

    /**
     * 清除SSO Token
     */
    public void clearSSOToken(String ssoToken) {
        try {
            Map<String, Object> claims = JwtUtils.parseToken(ssoToken);
            if (claims != null) {
                String tokenId = (String) claims.get("tokenId");
                String userId = claims.get("userId").toString();

                String tokenKey = SSO_TOKEN_PREFIX + tokenId;
                String userKey = SSO_USER_PREFIX + userId;

                redisService.deleteObject(tokenKey);
                redisService.deleteObject(userKey);

                log.info("清除SSO Token: {}", tokenId);
            }
        } catch (Exception e) {
            log.error("清除SSO Token失败", e);
        }
    }

    /**
     * 检查用户对系统的访问权限
     */
    public boolean checkSystemPermission(LoginUser loginUser, String targetSystem) {
        try {
            // 这里可以实现具体的权限检查逻辑
            // 例如：检查用户角色、部门权限等
            
            // 简单实现：检查系统是否已注册
            return REGISTERED_SYSTEMS.containsKey(targetSystem);

        } catch (Exception e) {
            log.error("检查系统权限失败", e);
            return false;
        }
    }

    /**
     * 获取用户权限信息
     */
    public Map<String, Object> getUserPermissions(LoginUser loginUser, String targetSystem) {
        Map<String, Object> permissions = new HashMap<>();
        
        try {
            // 获取用户角色
            permissions.put("roles", loginUser.getUser().getRoles());
            
            // 获取用户部门
            permissions.put("deptId", loginUser.getUser().getDeptId());
            
            // 根据目标系统返回特定权限
            permissions.put("systemPermissions", getSystemSpecificPermissions(loginUser, targetSystem));

        } catch (Exception e) {
            log.error("获取用户权限失败", e);
        }

        return permissions;
    }

    /**
     * 获取系统特定权限
     */
    private Map<String, Object> getSystemSpecificPermissions(LoginUser loginUser, String targetSystem) {
        Map<String, Object> systemPermissions = new HashMap<>();
        
        // 根据不同系统返回不同权限
        switch (targetSystem) {
            case "market":
                systemPermissions.put("canViewProducts", true);
                systemPermissions.put("canCreateOrders", true);
                break;
            default:
                systemPermissions.put("basicAccess", true);
        }
        
        return systemPermissions;
    }

    /**
     * 构造目标系统URL
     */
    public String buildTargetSystemUrl(String targetSystem, String ssoToken, String redirectUrl) {
        SystemConfig systemConfig = REGISTERED_SYSTEMS.get(targetSystem);
        if (systemConfig == null) {
            throw new RuntimeException("未知的目标系统: " + targetSystem);
        }

        StringBuilder url = new StringBuilder(systemConfig.getCallbackUrl());
        url.append("?token=").append(ssoToken);
        
        if (StringUtils.isNotEmpty(redirectUrl)) {
            url.append("&redirect=").append(redirectUrl);
        }

        return url.toString();
    }

    /**
     * 通知所有系统用户已登出
     */
    public void notifyAllSystemsLogout(String userId, String ssoToken) {
        for (SystemConfig systemConfig : REGISTERED_SYSTEMS.values()) {
            try {
                notifySystemLogout(systemConfig, userId, ssoToken);
            } catch (Exception e) {
                log.error("通知系统 {} 登出失败", systemConfig.getSystemId(), e);
            }
        }
    }

    /**
     * 通知单个系统用户已登出
     */
    private void notifySystemLogout(SystemConfig systemConfig, String userId, String ssoToken) {
        try {
            if (StringUtils.isEmpty(systemConfig.getLogoutUrl())) {
                return;
            }

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("userId", userId);
            requestBody.put("ssoToken", ssoToken);

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            restTemplate.postForEntity(systemConfig.getLogoutUrl(), entity, String.class);
            
            log.info("通知系统 {} 用户 {} 已登出", systemConfig.getSystemId(), userId);

        } catch (Exception e) {
            log.error("通知系统登出失败", e);
        }
    }

    /**
     * 系统配置类
     */
    public static class SystemConfig {
        private String systemId;
        private String systemName;
        private String callbackUrl;
        private String logoutUrl;

        public SystemConfig(String systemId, String systemName, String callbackUrl, String logoutUrl) {
            this.systemId = systemId;
            this.systemName = systemName;
            this.callbackUrl = callbackUrl;
            this.logoutUrl = logoutUrl;
        }

        // Getters
        public String getSystemId() { return systemId; }
        public String getSystemName() { return systemName; }
        public String getCallbackUrl() { return callbackUrl; }
        public String getLogoutUrl() { return logoutUrl; }
    }

    // 其他辅助方法...
    public void recordSSOLogin(LoginUser loginUser, String targetSystem, String ssoToken) {
        log.info("用户 {} 通过SSO登录到系统 {}", loginUser.getUsername(), targetSystem);
    }

    public void recordSSOLogout(String userId, String ssoToken) {
        log.info("用户 {} 通过SSO登出", userId);
    }

    public LoginUser getUserById(Long userId) {
        try {
            SysUser user = remoteUserService.getUserInfo(userId.toString(), "inner").getData();
            if (user != null) {
                LoginUser loginUser = new LoginUser();
                loginUser.setUserId(user.getUserId());
                loginUser.setUsername(user.getUserName());
                loginUser.setUser(user);
                return loginUser;
            }
        } catch (Exception e) {
            log.error("根据ID获取用户失败", e);
        }
        return null;
    }

    public boolean registerSystem(String systemId, String systemName, String callbackUrl, String logoutUrl) {
        try {
            REGISTERED_SYSTEMS.put(systemId, new SystemConfig(systemId, systemName, callbackUrl, logoutUrl));
            log.info("注册系统成功: {} - {}", systemId, systemName);
            return true;
        } catch (Exception e) {
            log.error("注册系统失败", e);
            return false;
        }
    }
}
