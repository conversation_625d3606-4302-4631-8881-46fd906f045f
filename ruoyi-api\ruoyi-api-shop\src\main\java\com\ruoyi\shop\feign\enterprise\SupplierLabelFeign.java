package com.ruoyi.shop.feign.enterprise;

import com.ruoyi.shop.entity.SupplierLabel;
import com.ruoyi.shop.result.QueryResult;
import com.ruoyi.shop.result.Result;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author:micah
 **/
public interface SupplierLabelFeign {

    /***
     * SupplierLabel分页条件搜索实现
     * @param supplierLabel
     * @param page
     * @param size
     * @return
     */
    @PostMapping(value = "/supplierLabel/search/{page}/{size}" )
    Result<QueryResult<SupplierLabel>> findPage(@RequestBody(required = false) SupplierLabel supplierLabel, @PathVariable("page") int page, @PathVariable("size") int size, @RequestParam(value = "fields", required = false) String fields);

    /***
     * 多条件搜索数据
     * @param supplierLabel
     * @return
     */
    @PostMapping(value = "/supplierLabel/search" )
    Result<List<SupplierLabel>> findList(@RequestBody(required = false) SupplierLabel supplierLabel, @RequestParam(value = "fields", required = false) String fields);

    /***
     * 根据ID删除数据
     * @param id
     * @return
     */
    @DeleteMapping(value = "/supplierLabel/{id}" )
    Result<Boolean> delete(@PathVariable("id") Long id);

    /***
     * 修改SupplierLabel数据
     * @param supplierLabel
     * @param id
     * @return
     */
    @PutMapping(value="/supplierLabel/{id}")
    Result<Boolean> update(@RequestBody SupplierLabel supplierLabel, @PathVariable("id") Long id);

    /***
     * 新增SupplierLabel数据
     * @param supplierLabel
     * @return
     */
    @PostMapping("/supplierLabel")
    Result<Boolean> add(@RequestBody SupplierLabel supplierLabel);

    /***
     * 批量新增SupplierLabel数据
     * @param supplierLabels
     * @return
     */
    @PostMapping("/supplierLabel/batch/add")
    Result<Boolean> batchAdd(@RequestBody List<SupplierLabel> supplierLabels);

    /***
     * 批量刪除SupplierLabel数据
     * @param opid
     * @return
     */
    @PostMapping("/supplierLabel/batch/delete")
    Result<Boolean> batchDelete(@RequestParam("opid") String opid);

    /***
     * 批量状态SupplierLabel数据
     * @param opid
     * @return
     */
    @PostMapping("/supplierLabel/state/op")
    Result<Boolean> stateOp(@RequestParam("opid") String opid, @RequestParam("status") Integer status);

    /***
     * 根据ID查询SupplierLabel数据
     * @param id
     * @return
     */
    @GetMapping("/supplierLabel/{id}")
    Result<SupplierLabel> findById(@PathVariable("id") Long id);
}