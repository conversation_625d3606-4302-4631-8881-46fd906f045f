package com.ruoyi.shop.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author:micah
 **/

@TableName("tb_inquiry_items")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InquiryItems extends Model<InquiryItems>{

	@TableId(value = "id", type = IdType.AUTO)
	private Long id;//id

	private Long inquiry_id;//询价id

	private String material_no;//物料编号

	private String material_name;//物料名称

	private String specs;//型号规格

	private BigDecimal quantity;//数量

	private String unit;//货物单位

	private String brand;//品牌

	private BigDecimal tax_price;//目标含税价格

	private String delivery_date;//期望交货日期

	private String attachment;//附件

	private Long classify_id;//一级分类ID

	private Long classify2_id;//二级分类ID

	private Long classify3_id;//三级分类ID

	private Integer version;//询价版本

	private String create_by;//创建者

	private Date create_time;//创建时间

	private String update_by;//更新者

	private Date update_time;//更新时间


}
