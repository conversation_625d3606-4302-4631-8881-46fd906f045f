package com.ruoyi.shop.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author:micah
 **/

@TableName("tb_order_deliver")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderDeliver extends Model<OrderDeliver>{

	@TableId(value = "id", type = IdType.AUTO)
	private Long id;//主键id

	private Long order_item_id;//订单明细id

	private Long order_id;//订单id

	private String linker;//联系人

	private String linkphone;//联系电话

	private String proof;//发货凭证

	private String sproof;//签收凭证

	private String carno;

	private String shipment_no;

	private String receipt_no;

	private BigDecimal shipped_qty;//发货数量

	private BigDecimal signed_qty;//签收数量

	private String signed_by;//签收人

	private String signed_time;//签收时间

	private String logistics_no;//物流单号

	private String status;//状态  UNSIGN未签收  SIGN 已签收

	private String create_by;//创建者

	private Date create_time;//创建时间

	private String update_by;//更新者

	private Date update_time;//更新时间

}
