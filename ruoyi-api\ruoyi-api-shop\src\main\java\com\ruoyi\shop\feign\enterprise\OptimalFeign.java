package com.ruoyi.shop.feign.enterprise;

import com.ruoyi.shop.entity.CreditRank;
import com.ruoyi.shop.entity.Optimal;
import com.ruoyi.shop.result.QueryResult;
import com.ruoyi.shop.result.Result;
import org.springframework.web.bind.annotation.*;

/**
 * @ClassName OptimalFeign
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/3/12
 * @Version 1.0
 */
public interface OptimalFeign {
    /***
     * 分页条件搜索实现
     * @param optimal
     * @param page
     * @param size
     * @return
     */
    @PostMapping(value = "/optimal/search/{page}/{size}" )
    Result<QueryResult<Optimal>> findPage(@RequestBody(required = false) Optimal optimal, @PathVariable("page") int page, @PathVariable("size") int size, @RequestParam(value = "fields", required = false) String fields);


    @PostMapping("/optimal")
    Result<Boolean> add(@RequestBody Optimal optimal);

    @PostMapping("/optimal/batch/delete")
    Result<Boolean> batchDelete(@RequestParam("opid") String opid);

    /***
     * 根据ID查询数据
     * @param id
     * @return
     */
    @GetMapping("/optimal/{id}")
    Result<Optimal> findById(@PathVariable("id") Long id);

    /***
     * 根据ID删除数据
     * @param id
     * @return
     */
    @DeleteMapping(value = "/optimal/{id}" )
    Result<Boolean> delete(@PathVariable("id") Long id);

    @PutMapping(value="/optimal/{id}")
    Result<Boolean> update(@RequestBody Optimal optimal, @PathVariable("id") Long id);
}
