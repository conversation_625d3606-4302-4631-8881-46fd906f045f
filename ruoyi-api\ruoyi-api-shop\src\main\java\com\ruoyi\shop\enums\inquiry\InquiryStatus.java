package com.ruoyi.shop.enums.inquiry;

/**
 * @ProjectName: ruoyi
 * @Package: com.ruoyi.shop.enums.inquiry
 * @ClassName: InquiryStatus
 * @Author: ${maguojun}
 * @Description: ${description}
 * @Date: 2022/5/19 15:08
 * @Version: 1.0
 */
public enum InquiryStatus {

    GOING("GOING","询价中"),
    DONE("DONE","已结束"),
    CLOSE("CLOSE","关闭询价");

    private final String key;
    private final String value;

    InquiryStatus(String key, String value){
        this.key = key;
        this.value = value;
    }
    //根据key获取枚举
    public static InquiryStatus getInquiryStatus(String key){
        if(null == key){
            return null;
        }
        for(InquiryStatus temp: InquiryStatus.values()){
            if(temp.getKey().equals(key)){
                return temp;
            }
        }
        return null;
    }

    public String getKey() {
        return key;
    }
    public String getValue() {
        return value;
    }
}
