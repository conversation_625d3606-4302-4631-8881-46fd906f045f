package com.ruoyi.shop.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:micah
 **/

@TableName("tb_product_info")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductInfo extends Model<ProductInfo>{

	@TableId(value = "id", type = IdType.AUTO)
	private Long id;//主键

	private Long product_id;//商品ID

	private String norms;//规格

	private String pictures;//产品图片

	private String details;//产品详情


}
