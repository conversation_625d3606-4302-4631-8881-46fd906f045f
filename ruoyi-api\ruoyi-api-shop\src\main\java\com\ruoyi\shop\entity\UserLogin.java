package com.ruoyi.shop.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.ruoyi.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * @Author:micah
 **/

@TableName("tb_user_login")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserLogin extends Model<UserLogin>{

	@TableId(value = "id", type = IdType.AUTO)
	private Long id;//ID

	private Long user_id;//用户ID

	private Integer err_count;

	private String login_state;

	private Date update_pwd_date;

	private Date last_login_date;
}
