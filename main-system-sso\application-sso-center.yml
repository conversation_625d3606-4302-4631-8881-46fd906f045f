# SSO认证中心配置
sso:
  # Token配置
  token:
    # Token过期时间（秒）- 2小时
    expire-time: 7200
    # Token密钥
    secret: ruoyi-sso-secret-key-2024
    # Token刷新时间（秒）- 30分钟
    refresh-time: 1800
  
  # 认证中心配置
  auth-center:
    # 认证中心标识
    center-id: main-system
    # 认证中心名称
    center-name: 智能共享后端系统
    # 认证中心地址
    center-url: http://localhost:8080
  
  # 已注册系统配置
  registered-systems:
    # 智能市场系统
    market:
      system-id: market
      system-name: 智能市场系统
      callback-url: http://localhost:8081/sso/login
      logout-url: http://localhost:8081/sso/logout
      status: active
      
    # 可以添加更多系统
    # another-system:
    #   system-id: another
    #   system-name: 另一个系统
    #   callback-url: http://localhost:8082/sso/login
    #   logout-url: http://localhost:8082/sso/logout
    #   status: active
  
  # 缓存配置
  cache:
    # 缓存前缀
    prefix: sso:center:
    # Token缓存前缀
    token-prefix: sso:token:
    # 用户缓存前缀
    user-prefix: sso:user:
    # 系统缓存前缀
    system-prefix: sso:system:
  
  # 安全配置
  security:
    # 允许的来源域名
    allowed-origins:
      - http://localhost:8080
      - http://localhost:8081
      - http://localhost:8082
    # Token签名算法
    sign-algorithm: HS256
    # 是否启用HTTPS
    enable-https: false

# RestTemplate配置
rest-template:
  # 连接超时时间（毫秒）
  connect-timeout: 5000
  # 读取超时时间（毫秒）
  read-timeout: 10000
  # 最大连接数
  max-connections: 100

# 日志配置
logging:
  level:
    com.ruoyi.auth.service.SSOAuthCenterService: INFO
    com.ruoyi.auth.controller.SSOAuthCenterController: INFO
