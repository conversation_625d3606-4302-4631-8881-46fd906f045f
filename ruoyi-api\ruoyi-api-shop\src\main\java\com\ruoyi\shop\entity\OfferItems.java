package com.ruoyi.shop.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author:micah
 **/

@TableName("tb_offer_items")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OfferItems extends Model<OfferItems>{

	@TableId(value = "id", type = IdType.AUTO)
	private Long id;//id

	private Long offer_id;//报价id

	private String offer_no;//报价单号

	private Long item_id;//询价明细ID

	private Long classify_id;//一级分类ID

	private Long classify2_id;//二级分类ID

	private Long classify3_id;//三级分类ID

	private String material_name;//物料名称

	private BigDecimal tax_price;//含税单价

	private BigDecimal total_price;//含税总价

	private String delivery_date;//预计交货日期

	private String warehouse;//仓库/发货地

	private String pack;//包装方式

	private BigDecimal freight;//运费

	private String attachment;//附件路径

	private Integer version;//报价版本

	private String create_by;//创建者

	private Date create_time;//创建时间

	private String update_by;//更新者

	private Date update_time;//更新时间

	private String delivery_type;//交货类型STOCK现货，FUTUR期货

	private BigDecimal tax_rate;//税率

	@TableField(exist = false)
	private String itemIds;

}
