package com.ruoyi.shop.feign.inquiry;

import com.ruoyi.shop.entity.Inquiry;
import com.ruoyi.shop.model.InquiryModel;
import com.ruoyi.shop.result.QueryResult;
import com.ruoyi.shop.result.Result;
import com.ruoyi.shop.result.TableResult;
import com.ruoyi.shop.vo.InquiryVO;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author:micah
 **/
public interface InquiryFeign {

    /**
     * 公开询价列表
     * @param inquiry_no
     * @param classify_id
     * @param classify2_id
     * @param classify3_id
     * @param title
     * @param enterprise_name
     * @param page
     * @param size
     * @return
     */
    @GetMapping(value = "/inquiry/open/list/{page}/{size}")
    Result<QueryResult<Inquiry>> queryOpenPage(@RequestParam(value = "inquiry_no", required = false) String inquiry_no,
                                               @RequestParam(value = "classify_id", required = false) Long classify_id,
                                               @RequestParam(value = "classify2_id", required = false) Long classify2_id,
                                               @RequestParam(value = "classify3_id", required = false) Long classify3_id,
                                               @RequestParam(value = "title", required = false) String title,
                                               @RequestParam(value = "enterprise_name", required = false) String enterprise_name,//采购商名称
                                               @RequestParam(value = "type", required = false) String type,
                                               @RequestParam(value = "status", required = false) String status,
                                               @RequestParam(value = "open", required = false) Integer open,
                                               @PathVariable("page") int page,
                                               @PathVariable("size") int size,
                                               @RequestParam(value = "inquiryIds", required = false) String inquiryIds);


    /**
     * 匹配询价列表
     * @param enterprise_id
     * @param inquiry_no
     * @param classify_id
     * @param classify2_id
     * @param classify3_id
     * @param title
     * @param enterprise_name
     * @param open
     * @param page
     * @param size
     * @return
     */
    @GetMapping(value = "/inquiry/self/list/{page}/{size}")
    Result<QueryResult<Inquiry>> querySelfPage(@RequestParam(value = "enterprise_id") Long enterprise_id,
                                               @RequestParam(value = "inquiry_no", required = false) String inquiry_no,
                                               @RequestParam(value = "classify_id", required = false) Long classify_id,
                                               @RequestParam(value = "classify2_id", required = false) Long classify2_id,
                                               @RequestParam(value = "classify3_id", required = false) Long classify3_id,
                                               @RequestParam(value = "title", required = false) String title,
                                               @RequestParam(value = "enterprise_name", required = false) String enterprise_name,//采购商名称
                                               @RequestParam(value = "type", required = false) String type,
                                               @RequestParam(value = "open", required = false) Integer open,
                                               @PathVariable("page") int page,
                                               @PathVariable("size") int size);


    /***
     * Inquiry分页条件搜索实现
     * @param inquiry
     * @param page
     * @param size
     * @return
     */
    @PostMapping(value = "/inquiry/search/{page}/{size}")
    Result<QueryResult<Inquiry>> findPage(@RequestBody(required = false) Inquiry inquiry, @PathVariable("page") int page, @PathVariable("size") int size, @RequestParam(value = "fields", required = false) String fields);

    /***
     * 多条件搜索数据
     * @param inquiry
     * @return
     */
    @PostMapping(value = "/inquiry/search")
    Result<List<Inquiry>> findList(@RequestBody(required = false) Inquiry inquiry, @RequestParam(value = "fields", required = false) String fields);

    /***
     * 根据ID删除数据
     * @param id
     * @return
     */
    @DeleteMapping(value = "/inquiry/{id}")
    Result<Boolean> delete(@PathVariable("id") Long id);

    /***
     * 修改Inquiry数据
     * @param inquiryModel
     * @param
     * @return
     */
    @PostMapping(value = "/inquiry/update")
    Result<Boolean> update(@RequestBody InquiryModel inquiryModel);

    /***
     * 新增Inquiry数据
     * @param inquiryModel
     * @return
     */
    @PostMapping(value = "/inquiry/save")
    Result<Boolean> add(@RequestBody InquiryModel inquiryModel);

    /***
     * 批量刪除Inquiry数据
     * @param opid
     * @return
     */
    @PostMapping("/inquiry/batch/delete")
    Result<Boolean> batchDelete(@RequestParam("opid") String opid);

    /***
     * 批量状态Inquiry数据
     * @param opid
     * @return
     */
    @PostMapping("/inquiry/state/op")
    Result<Boolean> stateOp(@RequestParam("opid") String opid, @RequestParam("status") String status);

    /***
     * 根据ID查询Inquiry数据
     * @param id
     * @return
     */
    @GetMapping("/inquiry/detail/{id}")
    Result<Inquiry> findById(@PathVariable("id") Long id);
}