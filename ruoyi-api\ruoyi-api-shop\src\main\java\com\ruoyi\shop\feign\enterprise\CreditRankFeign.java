package com.ruoyi.shop.feign.enterprise;

import com.ruoyi.shop.entity.CreditRank;
import com.ruoyi.shop.result.QueryResult;
import com.ruoyi.shop.result.Result;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author:micah
 **/
public interface CreditRankFeign {

    /***
     * CreditRank分页条件搜索实现
     * @param creditRank
     * @param page
     * @param size
     * @return
     */
    @PostMapping(value = "/creditRank/search/{page}/{size}" )
    Result<QueryResult<CreditRank>> findPage(@RequestBody(required = false) CreditRank creditRank, @PathVariable("page") int page, @PathVariable("size") int size, @RequestParam(value = "fields", required = false) String fields);

    /***
     * 多条件搜索数据
     * @param creditRank
     * @return
     */
    @PostMapping(value = "/creditRank/search" )
    Result<List<CreditRank>> findList(@RequestBody(required = false) CreditRank creditRank, @RequestParam(value = "fields", required = false) String fields);

    /***
     * 根据ID删除数据
     * @param id
     * @return
     */
    @DeleteMapping(value = "/creditRank/{id}" )
    Result<Boolean> delete(@PathVariable("id") Long id);

    /***
     * 修改CreditRank数据
     * @param creditRank
     * @param id
     * @return
     */
    @PutMapping(value="/creditRank/{id}")
    Result<Boolean> update(@RequestBody CreditRank creditRank, @PathVariable("id") Long id);

    /***
     * 新增CreditRank数据
     * @param creditRank
     * @return
     */
    @PostMapping("/creditRank")
    Result<Boolean> add(@RequestBody CreditRank creditRank);

    /***
     * 批量新增CreditRank数据
     * @param creditRanks
     * @return
     */
    @PostMapping("/creditRank/batch/add")
    Result<Boolean> batchAdd(@RequestBody List<CreditRank> creditRanks);

    /***
     * 批量刪除CreditRank数据
     * @param opid
     * @return
     */
    @PostMapping("/creditRank/batch/delete")
    Result<Boolean> batchDelete(@RequestParam("opid") String opid);

    /***
     * 批量状态CreditRank数据
     * @param opid
     * @return
     */
    @PostMapping("/creditRank/state/op")
    Result<Boolean> stateOp(@RequestParam("opid") String opid, @RequestParam("status") Integer status);

    /***
     * 根据ID查询CreditRank数据
     * @param id
     * @return
     */
    @GetMapping("/creditRank/{id}")
    Result<CreditRank> findById(@PathVariable("id") Long id);
}