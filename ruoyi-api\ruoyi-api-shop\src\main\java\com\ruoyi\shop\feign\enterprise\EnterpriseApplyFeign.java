package com.ruoyi.shop.feign.enterprise;

import com.ruoyi.shop.entity.EnterpriseApply;
import com.ruoyi.shop.result.QueryResult; import com.ruoyi.shop.result.Result;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author:micah
 **/
public interface EnterpriseApplyFeign {

    /***
     * EnterpriseApply分页条件搜索实现
     * @param enterpriseApply
     * @param page
     * @param size
     * @return
     */
    @PostMapping(value = "/enterprise/apply/search/{page}/{size}" )
    Result<QueryResult<EnterpriseApply>> findPage(@RequestBody(required = false) EnterpriseApply enterpriseApply, @PathVariable("page") int page, @PathVariable("size") int size, @RequestParam(value = "fields", required = false) String fields);

    /***
     * 多条件搜索数据
     * @param enterpriseApply
     * @return
     */
    @PostMapping(value = "/enterprise/apply/search" )
    Result<List<EnterpriseApply>> findList(@RequestBody(required = false) EnterpriseApply enterpriseApply, @RequestParam(value = "fields", required = false) String fields);

    /***
     * 根据ID删除数据
     * @param id
     * @return
     */
    @DeleteMapping(value = "/enterprise/apply/{id}" )
    Result<Boolean> delete(@PathVariable("id") Long id);

    /***
     * 修改EnterpriseApply数据
     * @param enterpriseApply
     * @param id
     * @return
     */
    @PutMapping(value="/enterprise/apply/{id}")
    Result<Boolean> update(@RequestBody EnterpriseApply enterpriseApply, @PathVariable("id") Long id);

    /***
     * 新增EnterpriseApply数据
     * @param enterpriseApply
     * @return
     */
    @PostMapping(value="/enterprise/apply")
    Result<Boolean> add(@RequestBody EnterpriseApply enterpriseApply);

    /***
     * 批量刪除EnterpriseApply数据
     * @param opid
     * @return
     */
    @PostMapping("/enterprise/apply/batch/delete")
    Result<Boolean> batchDelete(@RequestParam("opid") String opid);

    /***
     * 批量状态EnterpriseApply数据
     * @param id
     * @param remark
     * @return
     */
    @PostMapping("/enterprise/apply/state/op")
    Result<Boolean> stateOp(@RequestParam("id") Long id,
                            @RequestParam("status") String status,
                            @RequestParam(value = "remark",required = false) String remark);

    /***
     * 根据ID查询EnterpriseApply数据
     * @param id
     * @return
     */
    @GetMapping("/enterprise/apply/{id}")
    Result<EnterpriseApply> findById(@PathVariable("id") Long id);

    /**
     * 根据申请者删除
     * @param applyerId
     * @return
     */
    @DeleteMapping("/enterprise/apply/delete/applyer")
    Result<Boolean> deleteByApplyerId(@RequestParam("applyerId") Long applyerId);
}