package com.ruoyi.shop.enums.system;

/**
 * 字典枚举
 */
public enum DictType {

    IDENTIFICATION("product_identify","产品认证"),
    REGION("business_region","业务范围"),
    INFOR("infor","资讯类别");

    private final String key;
    private final String value;

    DictType(String key, String value){
        this.key = key;
        this.value = value;
    }
    //根据key获取枚举
    public static DictType getDictType(String key){
        if(null == key){
            return null;
        }
        for(DictType temp: DictType.values()){
            if(temp.getKey().equals(key)){
                return temp;
            }
        }
        return null;
    }

    public String getKey() {
        return key;
    }
    public String getValue() {
        return value;
    }
}
