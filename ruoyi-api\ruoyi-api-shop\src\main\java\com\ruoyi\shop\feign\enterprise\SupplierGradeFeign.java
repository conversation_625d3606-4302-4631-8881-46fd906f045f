package com.ruoyi.shop.feign.enterprise;


import com.ruoyi.shop.entity.SupplierGrade;
import com.ruoyi.shop.result.QueryResult;
import com.ruoyi.shop.result.Result;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author:micah
 **/
public interface SupplierGradeFeign {

    /***
     * SupplierGrade分页条件搜索实现
     * @param supplierGrade
     * @param page
     * @param size
     * @return
     */
    @PostMapping(value = "/supplierGrade/search/{page}/{size}" )
    Result<QueryResult<SupplierGrade>> findPage(@RequestBody(required = false) SupplierGrade supplierGrade, @PathVariable("page") int page, @PathVariable("size") int size, @RequestParam(value = "fields", required = false) String fields);

    /***
     * 多条件搜索数据
     * @param supplierGrade
     * @return
     */
    @PostMapping(value = "/supplierGrade/search" )
    Result<List<SupplierGrade>> findList(@RequestBody(required = false) SupplierGrade supplierGrade, @RequestParam(value = "fields", required = false) String fields);

    /***
     * 根据ID删除数据
     * @param id
     * @return
     */
    @DeleteMapping(value = "/supplierGrade/{id}" )
    Result<Boolean> delete(@PathVariable("id") Long id);

    /***
     * 修改SupplierGrade数据
     * @param supplierGrade
     * @param id
     * @return
     */
    @PutMapping(value="/supplierGrade/{id}")
    Result<Boolean> update(@RequestBody SupplierGrade supplierGrade, @PathVariable("id") Long id);

    /***
     * 新增SupplierGrade数据
     * @param supplierGrade
     * @return
     */
    @PostMapping("/supplierGrade")
    Result<Boolean> add(@RequestBody SupplierGrade supplierGrade);

    /***
     * 批量新增SupplierGrade数据
     * @param supplierGrades
     * @return
     */
    @PostMapping("/supplierGrade/batch/add")
    Result<Boolean> batchAdd(@RequestBody List<SupplierGrade> supplierGrades);

    /***
     * 批量刪除SupplierGrade数据
     * @param opid
     * @return
     */
    @PostMapping("/supplierGrade/batch/delete")
    Result<Boolean> batchDelete(@RequestParam("opid") String opid);

    /***
     * 批量状态SupplierGrade数据
     * @param opid
     * @return
     */
    @PostMapping("/supplierGrade/state/op")
    Result<Boolean> stateOp(@RequestParam("opid") String opid, @RequestParam("status") Integer status);

    /***
     * 根据ID查询SupplierGrade数据
     * @param id
     * @return
     */
    @GetMapping("/supplierGrade/{id}")
    Result<SupplierGrade> findById(@PathVariable("id") Long id);
}