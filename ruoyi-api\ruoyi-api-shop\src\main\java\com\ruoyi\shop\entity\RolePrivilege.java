package com.ruoyi.shop.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:micah
 **/

@TableName("tb_role_privilege")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RolePrivilege extends Model<RolePrivilege>{

	@TableId(value = "id", type = IdType.AUTO)
	private Long id;// 主键

	private Long role_id;//角色id

	private Long privilege_id;//菜单ID


}
