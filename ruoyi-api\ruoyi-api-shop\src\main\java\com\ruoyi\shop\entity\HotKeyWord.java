package com.ruoyi.shop.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:micah
 **/

@TableName("tb_hot_key_word")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HotKeyWord extends Model<HotKeyWord>{

	@TableId(value = "id", type = IdType.AUTO)
	private Long id;//

	private String value;//关键词

	private Long heat_value;//热度值


}
