package com.ruoyi.shop.feign.offer;

import com.ruoyi.shop.entity.Offer;
import com.ruoyi.shop.model.CountModel;
import com.ruoyi.shop.model.OfferModel;
import com.ruoyi.shop.result.QueryResult;
import com.ruoyi.shop.result.Result;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * @Author:micah
 **/

public interface OfferFeign {


    /***
     * 新增Offer数据
     * @param offerModel
     * @return
     */
    @PostMapping(value="/offer/save")
    Result<Boolean> save(@RequestBody OfferModel offerModel);

    /***
     * 多条件搜索数据
     * @param offer
     * @return
     */
    @PostMapping(value = "/offer/search" )
    Result<List<Offer>> findList(@RequestBody(required = false) Offer offer, @RequestParam(value = "fields", required = false) String fields);

    /***
     * 根据ID查询Offer数据
     * @param id
     * @return
     */
    @GetMapping("/offer/{id}")
    Result<Offer> findById(@PathVariable("id") Long id);

    /***
     * 修改Offer数据
     * @param offerModel
     * @return
     */
    @PostMapping(value="/offer/update")
    Result<Boolean> update(@RequestBody OfferModel offerModel);


    /**
     * 根据ID查询信息
     *
     * @param ids
     * @return
     */
    @GetMapping("/offer/ids")
    Result<List<Offer>> findByIds(@RequestParam("ids") String ids, @RequestParam(value = "fields", required = false) String fields);

    /***
     * Offer分页条件搜索实现
     * @param offer
     * @param page
     * @param size
     * @return
     */
    @PostMapping(value = "/offer/search/{page}/{size}" )
    Result<QueryResult<Offer>> findPage(@RequestBody(required = false) Offer offer, @PathVariable("page") int page, @PathVariable("size") int size, @RequestParam(value = "fields", required = false) String fields);

    /***
     * 根据ID删除数据
     * @param id
     * @return
     */
    @DeleteMapping(value = "/offer/{id}" )
    Result<Boolean> delete(@PathVariable("id") Long id);

    /**
     * 同步价格库
     * @param offerModel
     * @return
     */
    @PostMapping("/offer/sync")
    Result<Boolean> sync(@RequestBody OfferModel offerModel);

    /***
     * 批量新增Offer数据
     * @param offers
     * @return
     */
    @PostMapping("/offer/batch/add")
    Result<Boolean> batchAdd(@RequestBody List<Offer> offers);

    /***
     * 批量刪除Offer数据
     * @param opid
     * @return
     */
    @PostMapping("/offer/batch/delete")
    Result<Boolean> batchDelete(@RequestParam("opid") String opid);

    /***
     * 批量状态Offer数据
     * @param opid
     * @return
     */
    @PostMapping("/offer/state/op")
    Result<Boolean> stateOp(@RequestParam("opid") String opid, @RequestParam("status") Integer status);
}