package com.ruoyi.shop.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * @Author:micah
 **/
@TableName("tb_user_profile")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserProfile extends Model<UserProfile>{

	@TableId(value = "id", type = IdType.AUTO)
	private Long id;//主键

	private Long user_id;//用户ID

	private String realname;//姓名

	private String nickname;//昵称

	private String email;//邮箱

	private String avatar;//头像

	private Long dept_id;//部门ID

	private Long role_id;//角色ID

	private Long grade_id;//会员级别

	private String create_by;//创建者

	private Date create_time;//创建时间

	private String update_by;//更新者

	private Date update_time;//更新时间

	@TableField(exist = false)
	private List<Long> userIds;

	private String province;

	private String city;

	private String region;

	private String address;

}
