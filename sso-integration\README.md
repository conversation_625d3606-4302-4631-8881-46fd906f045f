# SSO单点登录集成方案

## 概述

本方案实现了 `share-intelligent-market`（智能市场系统）与 `share-intelligent-backend`（主系统）之间的单点登录（SSO）。

## 架构设计

```
主系统 (share-intelligent-backend)     市场系统 (share-intelligent-market)
        ↓                                      ↑
    [SSO认证中心]                          [SSO客户端]
        ↓                                      ↑
    生成SSO Token                         验证SSO Token
        ↓                                      ↑
    跳转到市场系统                         创建本地会话
```

## 实现流程

### 1. 用户登录流程

1. **用户在主系统登录**
   - 用户在主系统完成认证
   - 主系统生成SSO Token

2. **跳转到市场系统**
   - 主系统构造跳转URL：`http://market-system/sso/login?token=SSO_TOKEN&redirect=TARGET_URL`
   - 浏览器跳转到市场系统

3. **市场系统验证Token**
   - 市场系统接收SSO Token
   - 调用主系统验证接口验证Token有效性
   - 获取用户信息

4. **创建本地会话**
   - 根据主系统用户信息创建或更新本地用户
   - 生成本地系统的访问Token
   - 建立用户会话

### 2. 用户登出流程

1. **用户在任一系统登出**
2. **通知其他系统**
   - 调用其他系统的登出接口
   - 清除所有系统的用户会话

## 文件说明

### 后端文件

1. **SSOController.java** - SSO控制器
   - `/sso/login` - SSO登录入口
   - `/sso/logout` - SSO登出接口
   - `/sso/status` - 检查SSO状态
   - `/sso/loginUrl` - 获取主系统登录地址
   - `/sso/syncUser` - 用户信息同步

2. **SSOService.java** - SSO服务实现
   - `validateSSOToken()` - 验证SSO Token
   - `getOrCreateLocalUser()` - 获取或创建本地用户
   - `clearLocalUserSession()` - 清除本地会话

3. **SSOConfig.java** - SSO配置类
   - RestTemplate配置

4. **application-sso.yml** - SSO配置文件
   - 主系统地址配置
   - Token配置
   - 缓存配置

### 前端文件

1. **sso.js** - 前端SSO集成
   - `handleSSOLogin()` - 处理SSO登录
   - `redirectToMainSystemLogin()` - 跳转到主系统
   - `checkSSOStatus()` - 检查SSO状态
   - `ssoLogout()` - SSO登出

## 配置说明

### 1. 后端配置

在 `application.yml` 中添加SSO配置：

```yaml
sso:
  main-system:
    url: http://主系统地址:端口
    verify-token-url: /api/sso/verify
    login-url: /login
  current-system:
    system-id: market
    callback-url: http://当前系统地址:端口/sso/login
```

### 2. 前端配置

在 `main.js` 中引入SSO插件：

```javascript
import SSOPlugin from '@/utils/sso'
Vue.use(SSOPlugin)
```

## 部署步骤

### 1. 后端部署

1. 将SSO相关文件放入对应目录：
   - `SSOController.java` → `ruoyi-auth/src/main/java/com/ruoyi/auth/controller/`
   - `SSOService.java` → `ruoyi-auth/src/main/java/com/ruoyi/auth/service/`
   - `SSOConfig.java` → `ruoyi-auth/src/main/java/com/ruoyi/auth/config/`

2. 更新配置文件：
   - 将 `application-sso.yml` 内容合并到 `bootstrap.yml`

3. 添加依赖（如果需要）：
   ```xml
   <dependency>
       <groupId>org.springframework</groupId>
       <artifactId>spring-web</artifactId>
   </dependency>
   ```

### 2. 前端部署

1. 将 `sso.js` 放入 `src/utils/` 目录

2. 在 `main.js` 中引入SSO插件

3. 在路由守卫中添加SSO检查逻辑

## 安全考虑

1. **Token安全**
   - SSO Token应该有较短的有效期
   - 使用HTTPS传输
   - Token应该包含签名验证

2. **跨域安全**
   - 验证来源域名
   - 使用CORS配置

3. **会话管理**
   - 定期检查SSO状态
   - 实现会话超时机制

## 测试方案

### 1. 功能测试

1. **登录测试**
   - 在主系统登录后跳转到市场系统
   - 验证用户信息是否正确同步

2. **登出测试**
   - 在任一系统登出
   - 验证其他系统是否同步登出

3. **会话测试**
   - 验证Token过期处理
   - 验证并发登录处理

### 2. 性能测试

1. **并发测试**
   - 模拟多用户同时SSO登录
   - 验证系统性能

2. **缓存测试**
   - 验证Redis缓存效果
   - 测试缓存过期机制

## 故障排查

### 1. 常见问题

1. **Token验证失败**
   - 检查主系统地址配置
   - 检查网络连通性
   - 检查Token格式

2. **用户信息同步失败**
   - 检查用户映射逻辑
   - 检查数据库连接

3. **跨域问题**
   - 检查CORS配置
   - 检查域名配置

### 2. 日志查看

查看相关日志文件：
- SSO登录日志
- Token验证日志
- 用户同步日志

## 扩展功能

1. **多系统支持**
   - 支持更多系统接入SSO
   - 统一用户权限管理

2. **高级功能**
   - 单点登录记住我
   - 多因子认证集成
   - 用户行为审计
