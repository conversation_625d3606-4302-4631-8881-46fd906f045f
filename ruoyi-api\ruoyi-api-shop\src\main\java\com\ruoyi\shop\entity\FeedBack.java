package com.ruoyi.shop.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.lang.Long;
import java.util.Date;
import java.lang.String;
import java.lang.Integer;

/**
 * @Author:micah
 **/

@TableName("tb_feed_back")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FeedBack extends Model<FeedBack>{

	@TableId(value = "id", type = IdType.AUTO)
	private Long id;//主键id

	private String content;//反馈内容

	private Integer isdel;//是否删除

	private Integer status;//状态   0 未处理 1  已处理

	private String create_by;//创建者

	private Date create_time;//创建时间

	private String update_by;//更新者

	private Date update_time;//更新时间

	private String linkman;//联系人

	private String linkphone;//联系人手机号


}
