# Tomcat
server:
  port: 9704
  max-http-header-size: 5120KB
# Spring
spring:
  application:
    # 应用名称
    name: ruoyi-biz-shop
  servlet:
    multipart:
      enabled: true   # 启用http上传处理
      max-file-size: 1000MB # 设置单个文件的最大长度
      max-request-size: 1000MB # 设置最大的请求文件的大小
      file-size-threshold: 5MB  # 当上传文件达到5MB的时候进行磁盘写入
      location: /  # 上传的临时目录
  profiles:
    # 环境配置
    active: dev
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 127.0.0.1:8848
        username: nacos
        password: nacos
        namespace: ca6f4dcd-0826-4ad7-9eff-9180f9a832e1
      config:
        # 配置中心地址
        server-addr: 127.0.0.1:8848
        username: nacos
        password: nacos
        namespace: ca6f4dcd-0826-4ad7-9eff-9180f9a832e1
        file-extension: yaml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
  main:
    allow-bean-definition-overriding: true
