package com.ruoyi.shop.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Author:micah
 **/

@TableName("tb_infor")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Infor extends Model<Infor>{

	@TableId(value = "id", type = IdType.AUTO)
	private Long id;//主键

	private String title;//标题

	private Long category_id;//文章分类

	private String isdel;//是否删除

	private String create_by;//创建者

	private Date create_time;//创建时间

	private String update_by;//更新者

	private Date update_time;//更新时间


}
