package com.ruoyi.shop.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author:micah
 **/
@TableName("tb_pay_request")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PayRequest extends Model<PayRequest>{

	@TableId(value = "id", type = IdType.AUTO)
	private Long id;//主键id

	private Long purchase_id;//采购商id

	private Long supply_id;//供应商id

	private Long order_id;//订单id

	private String order_no;//订单编号

	private String request_no;//付款申请单编号

	private BigDecimal total_amount;//含税总金额

	private String payment;//付款方式  ONLINE-线上支付 PREPAY-预付定金  PREALL预付全款 OFFLINE线下支付 PERIOD账期付款

	private Integer period;//账期

	private String currency;//币种

	private BigDecimal pay_ratio;//付款比例

	private BigDecimal pay_amount;//付款金额

	private String payno;//檬豆宝支付单号

	private String refundno;//退款单号

	private String pay_proof;//付款凭证

	private Date paying_time;//提醒支付时间

	private String pay_status;//状态 枚举

	private String openbank;//开户行

	private String cardno;//银行账户

	private String account;//户头

	private String bank;//开户地址

	private Date pay_time;//付款时间

	private Date refund_time;//申请退款时间

	private Date refunded_time;//确认退款时间

	private String refund_proof;//退款凭证

	private Date create_time;//创建时间

	private String create_by;//创建人

	private Date update_time;//更新时间

	private String update_by;//更新人

	@TableField(exist = false)
	private String source;//来源

	@TableField(exist = false)
	private Date deadline;//团购截止日期

}
