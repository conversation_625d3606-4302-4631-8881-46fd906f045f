package com.ruoyi.shop.feign.enterprise;

import com.ruoyi.shop.entity.CreditRating;
import com.ruoyi.shop.result.QueryResult;
import com.ruoyi.shop.result.Result;
import org.springframework.web.bind.annotation.*;


/**
 * @ClassName CreditRatingFeign
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/12/14
 * @Version 1.0
 */
public interface CreditRatingFeign {
    /***
     * 分页条件搜索实现
     * @param creditRating
     * @param page
     * @param size
     * @return
     */
    @PostMapping(value = "/creditRating/search/{page}/{size}" )
    Result<QueryResult<CreditRating>> findPage(@RequestBody(required = false) CreditRating creditRating, @PathVariable("page") int page, @PathVariable("size") int size, @RequestParam(value = "fields", required = false) String fields);


    @PostMapping("/creditRating")
    Result<Boolean> add(@RequestBody CreditRating creditRating);

    @PostMapping("/creditRating/batch/delete")
    Result<Boolean> batchDelete(@RequestParam("opid") String opid);

    /***
     * 根据ID查询数据
     * @param id
     * @return
     */
    @GetMapping("/creditRating/{id}")
    Result<CreditRating> findById(@PathVariable("id") Long id);

    /***
     * 修改数据
     * @param creditRating
     * @return
     */
    @PutMapping(value="/creditRating/{id}")
    Result<Boolean> update(@RequestBody CreditRating creditRating, @PathVariable("id") Long id);


    /***
     * 根据ID删除数据
     * @param id
     * @return
     */
    @DeleteMapping(value = "/creditRating/{id}" )
    Result<Boolean> delete(@PathVariable("id") Long id);
}
