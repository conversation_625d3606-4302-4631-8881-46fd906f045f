package com.ruoyi.shop.feign.infor;

import com.ruoyi.shop.entity.Infor;
import com.ruoyi.shop.model.InforModel;
import com.ruoyi.shop.result.QueryResult; import com.ruoyi.shop.result.Result;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author:micah
 **/

public interface InforFeign {

    /***
     * Infor分页条件搜索实现
     * @param infor
     * @param page
     * @param size
     * @return
     */
    @PostMapping(value = "/infor/search/{page}/{size}" )
    Result<QueryResult<Infor>> findPage(@RequestBody(required = false) Infor infor, @PathVariable("page") int page, @PathVariable("size") int size, @RequestParam(value = "fields", required = false) String fields);

    /***
     * 多条件搜索数据
     * @param infor
     * @return
     */
    @PostMapping(value = "/infor/search" )
    Result<List<Infor>> findList(@RequestBody(required = false) Infor infor, @RequestParam(value = "fields", required = false) String fields);

    /***
     * 修改Infor数据
     * @return
     */
    @PutMapping(value="/infor/update")
    Result<Boolean> update(@RequestBody InforModel inforModel);

    /***
     * 新增Infor数据
     * @return
     */
    @PostMapping(value="/infor/save")
    Result<Long> add(@RequestBody InforModel inforModel);

    /***
     * 批量刪除Infor数据
     * @param opid
     * @return
     */
    @PostMapping("/infor/batch/delete")
    Result<Boolean> batchDelete(@RequestParam("opid") String opid);

    /***
     * 根据ID查询Infor数据
     * @param id
     * @return
     */
    @GetMapping("/infor/{id}")
    Result<Infor> findById(@PathVariable("id") Long id);
}