package com.ruoyi.shop.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author:micah
 **/

@TableName("tb_product_cart")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductCart extends Model<ProductCart>{

	@TableId(value = "id", type = IdType.AUTO)
	private Long id;//主键

	private Long user_id;//用户ID

	private Long product_id;//产品ID

	private Long enterprise_id;//企业ID

	private BigDecimal number;//数量

	private Date create_time;//加购时间

}
