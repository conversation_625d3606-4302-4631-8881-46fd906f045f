package com.ruoyi.shop.feign.order;

import com.ruoyi.shop.entity.OrderFiles;
import com.ruoyi.shop.result.QueryResult;
import com.ruoyi.shop.result.Result;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author:micah
 **/

public interface OrderFilesFeign {

    /***
     * OrderFiles分页条件搜索实现
     * @param orderFiles
     * @param page
     * @param size
     * @return
     */
    @PostMapping(value = "/order/files/search/{page}/{size}" )
    Result<QueryResult<OrderFiles>> findPage(@RequestBody(required = false) OrderFiles orderFiles, @PathVariable("page") int page, @PathVariable("size") int size, @RequestParam(value = "fields", required = false) String fields);

    @PostMapping(value = "/order/files/searchPageData/{page}/{size}" )
    Result<QueryResult<OrderFiles>> searchPageData(@RequestBody(required = false) OrderFiles orderFiles, @PathVariable("page") int page, @PathVariable("size") int size, @RequestParam(value = "fields", required = false) String fields);

    /***
     * 多条件搜索数据
     * @param orderFiles
     * @return
     */
    @PostMapping(value = "/order/files/search" )
    Result<List<OrderFiles>> findList(@RequestBody(required = false) OrderFiles orderFiles, @RequestParam(value = "fields", required = false) String fields);

    /***
     * 根据ID删除数据
     * @param id
     * @return
     */
    @DeleteMapping(value = "/order/files/{id}" )
    Result<Boolean> delete(@PathVariable("id") Long id);


    /***
     * 根据orderId删除数据
     * @param id
     * @return
     */
    @PostMapping(value = "/order/files/deleteByOrderId/{id}" )
    Result<Boolean> deleteByOrderId(@PathVariable("id") Long id);


    /***
     * 新增OrderFiles数据
     * @param orderFiles
     * @return
     */
    @PostMapping(value = "/order/files")
    Result<Boolean> add(@RequestBody OrderFiles orderFiles);

    @PutMapping(value="/order/files/update")
    Result<Boolean> update(@RequestBody OrderFiles orderFiles);
}