package com.ruoyi.shop.feign.order;

import com.ruoyi.shop.entity.PayRequest;
import com.ruoyi.shop.result.QueryResult; import com.ruoyi.shop.result.Result;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * @Author:micah
 **/
public interface PayRequestFeign {

    /***
     * PayRequest分页条件搜索实现
     * @param payRequest
     * @param page
     * @param size
     * @return
     */
    @PostMapping(value = "/payrequest/search/{page}/{size}" )
    Result<QueryResult<PayRequest>> findPage(@RequestBody(required = false) PayRequest payRequest, @PathVariable("page") int page, @PathVariable("size") int size, @RequestParam(value = "fields", required = false) String fields);

    /***
     * 多条件搜索数据
     * @param payRequest
     * @return
     */
    @PostMapping(value = "/payrequest/search" )
    Result<List<PayRequest>> findList(@RequestBody(required = false) PayRequest payRequest, @RequestParam(value = "fields", required = false) String fields);

    /***
     * 修改PayRequest数据
     * @param payRequest
     * @param id
     * @return
     */
    @PutMapping(value="/payrequest/{id}")
    Result<Boolean> update(@RequestBody PayRequest payRequest, @PathVariable("id") Long id);

    /**
     * 修改支付单号
     * @param requestNo
     * @param payNo
     * @return
     */
    @PostMapping(value="/payrequest/payno/update")
    Result<Boolean> updatePayno(@RequestParam("requestNo") String requestNo,@RequestParam("payNo") String payNo);


    /**
     * 批量修改支付单号
     * @body list
     * @return
     */
    @PostMapping(value="/payrequest/payno/list/update")
    Result<Boolean> updatePaynoList(@RequestBody List<PayRequest> list);

    /**
     * 修改退款单号
     * @param requestNo
     * @param refundNo
     * @return
     */
    @PostMapping(value="/payrequest/refundno/update")
    Result<Boolean> updateRefundno(@RequestParam("requestNo") String requestNo,@RequestParam("refundNo") String refundNo);


    /**
     * 修改退款单号
     * @param orderNo
     * @param refundNo
     * @return
     */
    @PostMapping(value="/payrequest/refundno/alipay/update")
    Result<Boolean> updateRefundnoByOrderNo(@RequestParam("orderNo") String orderNo,@RequestParam("refundNo") String refundNo);

    /***
     * 新增PayRequest数据
     * @param payRequest
     * @return
     */
    @PostMapping(value = "/payrequest/save")
    Result<Boolean> add(@RequestBody PayRequest payRequest);
    /***
     * 批量刪除PayRequest数据
     * @param opid
     * @return
     */
    @PostMapping("/payrequest/batch/delete")
    Result<Boolean> batchDelete(@RequestParam("opid") String opid);

    /***
     * 根据ID查询PayRequest数据
     * @param id
     * @return
     */
    @GetMapping("/payrequest/{id}")
    Result<PayRequest> findById(@PathVariable("id") Long id);

    /***
     * 线上支付
     * @param payRequest
     * @return
     */
    @PostMapping(value = "/payrequest/pay")
    Result<Boolean> pay(@RequestBody PayRequest payRequest);

    /***
     * 线上退款
     * @param payRequest
     * @return
     */
    @PostMapping(value = "/payrequest/refund")
    Result<Boolean> refund(@RequestBody PayRequest payRequest);

    /***
     * 根据requestNo查询PayRequest数据
     * @param requestNo
     * @return
     */
    @GetMapping("/payrequest/no")
    Result<PayRequest> findByNo(@RequestParam("requestNo") String requestNo,@RequestParam(value = "field", required = false) String field);

    /**
     * 根据 requestNos 付款申请单
     * 查询付款申请单
     * 支持批量查询
     *
     * @param requestNos
     * @return
     */
    @GetMapping("/payrequest/ids")
    Result<List<PayRequest>> findByNos(@RequestParam("requestNos") String requestNos, @RequestParam(value = "fields", required = false) String fields);
}
