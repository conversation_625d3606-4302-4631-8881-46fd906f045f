package com.ruoyi.shop.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:micah
 **/

@TableName("tb_supplier_label_relation")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SupplierLabelRelation extends Model<SupplierLabelRelation>{

	@TableId(value = "id", type = IdType.AUTO)
	private Long id;//

	private Long supplier_id;//供应商id

	private Long label_id;//标签id


}
