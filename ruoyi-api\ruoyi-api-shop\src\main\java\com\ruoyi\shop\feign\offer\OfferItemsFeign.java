package com.ruoyi.shop.feign.offer;

import com.ruoyi.shop.entity.OfferItems;
import com.ruoyi.shop.result.QueryResult; import com.ruoyi.shop.result.Result;
import com.ruoyi.shop.vo.OfferAllVO;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author:micah
 **/

public interface OfferItemsFeign {

    /***
     * OfferItems分页条件搜索实现
     * @param offerItems
     * @param page
     * @param size
     * @return
     */
    @PostMapping(value = "/offer/items/search/{page}/{size}" )
    Result<QueryResult<OfferItems>> findPage(@RequestBody(required = false) OfferItems offerItems, @PathVariable("page") int page, @PathVariable("size") int size, @RequestParam(value = "fields", required = false) String fields);

    /***
     * 多条件搜索数据
     * @param offerItems
     * @return
     */
    @PostMapping(value = "/offer/items/search" )
    Result<List<OfferItems>> findList(@RequestBody(required = false) OfferItems offerItems, @RequestParam(value = "fields", required = false) String fields);

    /***
     * 根据ID删除数据
     * @param id
     * @return
     */
    @DeleteMapping(value = "/offer/items/{id}" )
    Result<Boolean> delete(@PathVariable("id") Long id);

    /***
     * 修改OfferItems数据
     * @param offerItems
     * @param id
     * @return
     */
    @PutMapping(value="/offer/items/{id}")
    Result<Boolean> update(@RequestBody OfferItems offerItems, @PathVariable("id") Long id);

    /***
     * 新增OfferItems数据
     * @param offerItems
     * @return
     */
    @PostMapping(value="/offer/items")
    Result<Boolean> add(@RequestBody OfferItems offerItems);

    /***
     * 批量刪除OfferItems数据
     * @param opid
     * @return
     */
    @PostMapping("/offer/items/batch/delete")
    Result<Boolean> batchDelete(@RequestParam("opid") String opid);

    /***
     * 批量状态OfferItems数据
     * @param opid
     * @return
     */
    @PostMapping("/offer/items/state/op")
    Result<Boolean> stateOp(@RequestParam("opid") String opid, @RequestParam("status") Integer status);

    /***
     * 根据ID查询OfferItems数据
     * @param id
     * @return
     */
    @GetMapping("/offer/items/{id}")
    Result<OfferItems> findById(@PathVariable("id") Long id);

    /**
     * 查询所有报价
     *
     * @param itemId 询价明细ID
     * @param version 报价版本
     * @return 查询结果
     */
    @GetMapping("/offer/items/search/offer")
    Result<List<OfferAllVO>> findOfferAll(@RequestParam("item_id") Long itemId, @RequestParam("version") Integer version);
}