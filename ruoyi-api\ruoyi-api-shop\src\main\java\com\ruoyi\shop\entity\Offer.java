package com.ruoyi.shop.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Author:micah
 **/

@TableName("tb_offer")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Offer extends Model<Offer>{

	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	private Long inquiry_id;//询价ID

	private String inquiry_no;//询价单号

	private String offer_no;//报价单号

	private Long enterprise_id;//报价公司ID

	private String enterprise_name;//报价公司名称

	private String linker;//联系人

	private String linkphone;//联系电话

	private String remark;//备注

	private String other_fee;//其他费用

	private String other_fee_v2;//其他费用

	private String create_by;//创建者

	private Date create_time;//创建时间

	private String update_by;//更新者

	private Date update_time;//更新时间

	private Integer version;//询价版本

	private String operator;

	@TableField(exist = false)
	private String inquiryIds;

	@TableField(exist = false)
	private String offerIds;

}
