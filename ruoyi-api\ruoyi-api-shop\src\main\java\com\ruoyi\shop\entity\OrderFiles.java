package com.ruoyi.shop.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * @Author:micah
 **/

@TableName("tb_order_files")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderFiles extends Model<OrderFiles>{

	@TableId(value = "id", type = IdType.AUTO)
	private Long id;//id

	private Long order_id;//订单id

	private String file_name;

	private String file_url;

	private String create_by;//创建者

	private Date create_time;//创建时间

	private String update_by;//更新者

	private Date update_time;//更新时间

	private String remark;//备注

	@TableField(exist = false)
	private List<Long> orderIds;

}
