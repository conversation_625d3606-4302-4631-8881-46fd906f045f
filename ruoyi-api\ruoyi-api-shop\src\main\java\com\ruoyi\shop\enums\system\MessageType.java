package com.ruoyi.shop.enums.system;

/**
 * 导航图枚举
 */
public enum MessageType {

    enterpriseApply("0","供应商入驻审核"),
    product("1","商品审核"),
    offer("2","报价完成向采购商发消息"),
    inquiry("3","采购商询价向供应商发消息"),
    place("9","采购商下单向供应商发消息"),
    payRequest("4","采购商支付向供应商发消息"),
    order("5","供应商确认订单向采购商发消息"),
    shipped("6","供应商发货向采购商发消息"),
    signed("7","采购商签收向供应商发消息"),
    system("8","管理平台发送系统公告给每个人发消息");

    private final String key;
    private final String value;

    MessageType(String key, String value){
        this.key = key;
        this.value = value;
    }
    //根据key获取枚举
    public static MessageType getMessageType(String key){
        if(null == key){
            return null;
        }
        for(MessageType temp: MessageType.values()){
            if(temp.getKey().equals(key)){
                return temp;
            }
        }
        return null;
    }

    public String getKey() {
        return key;
    }
    public String getValue() {
        return value;
    }
}
