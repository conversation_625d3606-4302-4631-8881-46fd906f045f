package com.ruoyi.shop.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Author:micah
 **/

@TableName("tb_privilege")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Privilege extends Model<Privilege>{

	@TableId(value = "id", type = IdType.AUTO)
	private Long id;//主键ID

	private String type;//类型  类型 PURCHASE-采购商 SUPPLY-供应商

	private String name;//名称

	private Long pid;//上级ID

	private Integer sorts;//排序

	private String permission;//权限标志

	private String create_by;//创建者

	private Date create_time;//创建时间

	private String update_by;//更新者

	private Date update_time;//更新时间


}
