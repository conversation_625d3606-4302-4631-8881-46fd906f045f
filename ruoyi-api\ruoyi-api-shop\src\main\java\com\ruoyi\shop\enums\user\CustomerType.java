package com.ruoyi.shop.enums.user;

/**
 * 客服类型
 */
public enum CustomerType {

    PLATFORM("P","平台客服"),
    STORE("S","店铺客服");

    private final String key;
    private final String value;

    CustomerType(String key, String value){
        this.key = key;
        this.value = value;
    }
    //根据key获取枚举
    public static CustomerType getCustomerType(String key){
        if(null == key){
            return null;
        }
        for(CustomerType temp: CustomerType.values()){
            if(temp.getKey().equals(key)){
                return temp;
            }
        }
        return null;
    }

    public String getKey() {
        return key;
    }
    public String getValue() {
        return value;
    }
}
