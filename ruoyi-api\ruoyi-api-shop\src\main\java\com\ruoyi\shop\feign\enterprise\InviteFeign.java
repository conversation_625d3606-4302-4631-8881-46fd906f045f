package com.ruoyi.shop.feign.enterprise;

import com.ruoyi.shop.entity.Invite;
import com.ruoyi.shop.result.QueryResult;
import com.ruoyi.shop.result.Result;
import org.springframework.web.bind.annotation.*;

/**
 * @ClassName InviteFeign
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/3/12
 * @Version 1.0
 */
public interface InviteFeign {
    /***
     * 分页条件搜索实现
     * @param invite
     * @param page
     * @param size
     * @return
     */
    @PostMapping(value = "/invite/search/{page}/{size}" )
    Result<QueryResult<Invite>> findPage(@RequestBody(required = false) Invite invite, @PathVariable("page") int page, @PathVariable("size") int size, @RequestParam(value = "fields", required = false) String fields);


    @PostMapping("/invite")
    Result<Boolean> add(@RequestBody Invite invite);

    @PostMapping("/invite/batch/delete")
    Result<Boolean> batchDelete(@RequestParam("opid") String opid);

    /***
     * 根据ID查询数据
     * @param id
     * @return
     */
    @GetMapping("/invite/{id}")
    Result<Invite> findById(@PathVariable("id") Long id);

    /***
     * 根据ID删除数据
     * @param id
     * @return
     */
    @DeleteMapping(value = "/invite/{id}" )
    Result<Boolean> delete(@PathVariable("id") Long id);

    @PostMapping(value = "/invite/findOptimalPage/{page}/{size}" )
    Result<QueryResult<Invite>> findOptimalPage(@RequestBody(required = false) Invite invite, @PathVariable("page") int page, @PathVariable("size") int size, @RequestParam(value = "fields", required = false) String fields);

}
