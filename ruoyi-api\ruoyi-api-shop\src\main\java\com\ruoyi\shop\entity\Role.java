package com.ruoyi.shop.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Author:micah
 **/

@TableName("tb_role")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Role extends Model<Role>{

	@TableId(value = "id", type = IdType.AUTO)
	private Long id;// 主键ID

	private String name;//名称

	private String remark;//备注

	private String create_by;//创建者

	private Date create_time;//创建时间

	private String update_by;//更新者

	private Date update_time;//更新时间

	private Long enterprise_id;//公司id

	@TableField(exist = false)
	private String permissions;//权限

	@TableField(exist = false)
	private String roleIds;//角色id


}
