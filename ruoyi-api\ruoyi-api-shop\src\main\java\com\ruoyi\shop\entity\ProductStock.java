package com.ruoyi.shop.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.lang.Long;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.String;

/**
 * @Author:micah
 **/

@TableName("tb_product_stock")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductStock extends Model<ProductStock>{

	@TableId(value = "id", type = IdType.AUTO)
	private Long id;//主键id

	private Long product_id;//商品id

	private String product_name;//商品名称

	private BigDecimal num;//商品数量

	private Long enterprise_id;//企业id

	private String enterprise_name;//企业名称

	private String type;//0出库 1入库

	private String create_by;//创建者

	private Date create_time;//创建时间

	private String update_by;//更新者

	private Date update_time;//更新时间

	private String debit;//收方名称/入方名称


}
