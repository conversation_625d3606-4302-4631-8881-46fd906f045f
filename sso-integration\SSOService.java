package com.ruoyi.auth.service;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.service.TokenService;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.model.LoginUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * SSO服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class SSOService {

    private static final Logger log = LoggerFactory.getLogger(SSOService.class);

    @Autowired
    private RedisService redisService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private RestTemplate restTemplate;

    // 主系统配置
    @Value("${sso.main-system.url:http://localhost:8080}")
    private String mainSystemUrl;

    @Value("${sso.main-system.verify-token-url:/api/sso/verify}")
    private String verifyTokenUrl;

    @Value("${sso.main-system.login-url:/login}")
    private String loginUrl;

    // SSO缓存配置
    private static final String SSO_TOKEN_PREFIX = "sso:token:";
    private static final String SSO_USER_PREFIX = "sso:user:";
    private static final long SSO_CACHE_EXPIRE = 7200; // 2小时

    /**
     * 验证SSO Token
     * 
     * @param ssoToken SSO Token
     * @return 用户信息，验证失败返回null
     */
    public JSONObject validateSSOToken(String ssoToken) {
        try {
            // 1. 先从缓存中查找
            String cacheKey = SSO_TOKEN_PREFIX + ssoToken;
            JSONObject cachedUserInfo = redisService.getCacheObject(cacheKey);
            if (cachedUserInfo != null) {
                log.info("从缓存中获取SSO用户信息: {}", cachedUserInfo.getString("username"));
                return cachedUserInfo;
            }

            // 2. 调用主系统验证接口
            String verifyUrl = mainSystemUrl + verifyTokenUrl;
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("ssoToken", ssoToken);
            requestBody.put("targetSystem", "market");
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
            
            ResponseEntity<JSONObject> response = restTemplate.postForEntity(verifyUrl, entity, JSONObject.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                JSONObject result = response.getBody();
                if (result.getInteger("code") == 200) {
                    JSONObject userInfo = result.getJSONObject("data");
                    
                    // 3. 缓存验证结果
                    redisService.setCacheObject(cacheKey, userInfo, SSO_CACHE_EXPIRE, TimeUnit.SECONDS);
                    
                    log.info("SSO Token验证成功: {}", userInfo.getString("username"));
                    return userInfo;
                }
            }
            
            log.warn("SSO Token验证失败: {}", ssoToken);
            return null;

        } catch (Exception e) {
            log.error("验证SSO Token异常", e);
            return null;
        }
    }

    /**
     * 根据主系统用户信息获取或创建本地用户
     * 
     * @param userInfo 主系统用户信息
     * @return 本地用户信息
     */
    public LoginUser getOrCreateLocalUser(JSONObject userInfo) {
        try {
            String mainUserId = userInfo.getString("userId");
            String username = userInfo.getString("username");
            String phone = userInfo.getString("phone");
            String email = userInfo.getString("email");

            // 1. 尝试通过用户名查找本地用户
            SysUser localUser = remoteUserService.getUserInfo(username, "inner").getData();
            
            if (localUser == null && StringUtils.isNotEmpty(phone)) {
                // 2. 尝试通过手机号查找
                localUser = remoteUserService.getUserByPhone(phone, "inner").getData();
            }

            if (localUser == null && StringUtils.isNotEmpty(email)) {
                // 3. 尝试通过邮箱查找
                localUser = remoteUserService.getUserByEmail(email, "inner").getData();
            }

            if (localUser == null) {
                // 4. 创建新用户
                localUser = createLocalUser(userInfo);
            } else {
                // 5. 更新用户信息
                updateLocalUser(localUser, userInfo);
            }

            // 6. 构造LoginUser对象
            LoginUser loginUser = new LoginUser();
            loginUser.setUserId(localUser.getUserId());
            loginUser.setUsername(localUser.getUserName());
            loginUser.setUser(localUser);
            
            // 7. 缓存SSO用户映射关系
            String userCacheKey = SSO_USER_PREFIX + mainUserId;
            redisService.setCacheObject(userCacheKey, localUser.getUserId(), SSO_CACHE_EXPIRE, TimeUnit.SECONDS);

            return loginUser;

        } catch (Exception e) {
            log.error("获取或创建本地用户失败", e);
            return null;
        }
    }

    /**
     * 创建本地用户
     */
    private SysUser createLocalUser(JSONObject userInfo) {
        // 实现用户创建逻辑
        SysUser user = new SysUser();
        user.setUserName(userInfo.getString("username"));
        user.setNickName(userInfo.getString("nickname"));
        user.setPhonenumber(userInfo.getString("phone"));
        user.setEmail(userInfo.getString("email"));
        user.setStatus("0"); // 正常状态
        user.setRemark("SSO自动创建用户");
        
        // 调用远程服务创建用户
        // remoteUserService.insertUser(user, "inner");
        
        return user;
    }

    /**
     * 更新本地用户信息
     */
    private void updateLocalUser(SysUser localUser, JSONObject userInfo) {
        // 实现用户信息更新逻辑
        boolean needUpdate = false;
        
        if (!userInfo.getString("nickname").equals(localUser.getNickName())) {
            localUser.setNickName(userInfo.getString("nickname"));
            needUpdate = true;
        }
        
        if (needUpdate) {
            // remoteUserService.updateUser(localUser, "inner");
        }
    }

    /**
     * 记录SSO登录日志
     */
    public void recordSSOLogin(LoginUser loginUser, String ssoToken) {
        try {
            log.info("用户 {} 通过SSO登录成功", loginUser.getUsername());
            // 可以在这里记录登录日志到数据库
        } catch (Exception e) {
            log.error("记录SSO登录日志失败", e);
        }
    }

    /**
     * 清除本地用户会话
     */
    public boolean clearLocalUserSession(String userId, String ssoToken) {
        try {
            // 1. 清除SSO相关缓存
            String tokenCacheKey = SSO_TOKEN_PREFIX + ssoToken;
            String userCacheKey = SSO_USER_PREFIX + userId;
            
            redisService.deleteObject(tokenCacheKey);
            redisService.deleteObject(userCacheKey);

            // 2. 清除本地用户Token（如果需要强制登出）
            // tokenService.delLoginUser(userId);

            log.info("清除用户 {} 的SSO会话成功", userId);
            return true;

        } catch (Exception e) {
            log.error("清除用户会话失败", e);
            return false;
        }
    }

    /**
     * 检查SSO状态
     */
    public boolean checkSSOStatus(LoginUser loginUser) {
        try {
            // 检查用户的SSO状态是否有效
            String userCacheKey = SSO_USER_PREFIX + loginUser.getUserId();
            return redisService.hasKey(userCacheKey);
        } catch (Exception e) {
            log.error("检查SSO状态失败", e);
            return false;
        }
    }

    /**
     * 获取主系统登录地址
     */
    public String getMainSystemLoginUrl(String redirectUrl) {
        String url = mainSystemUrl + loginUrl;
        if (StringUtils.isNotEmpty(redirectUrl)) {
            url += "?redirect=" + redirectUrl;
        }
        return url;
    }

    /**
     * 同步用户信息
     */
    public boolean syncUserInfo(JSONObject userInfo) {
        try {
            // 实现用户信息同步逻辑
            log.info("同步用户信息: {}", userInfo);
            return true;
        } catch (Exception e) {
            log.error("同步用户信息失败", e);
            return false;
        }
    }
}
