package com.ruoyi.shop.feign.order;

import com.ruoyi.shop.entity.AfterSale;
import com.ruoyi.shop.result.QueryResult;
import com.ruoyi.shop.result.Result;
import org.springframework.web.bind.annotation.*;

/**
 * @ClassName AfterSaleFeign
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/12/19
 * @Version 1.0
 */
public interface AfterSaleFeign {

    /***
     * 分页条件搜索实现
     * @param afterSale
     * @param page
     * @param size
     * @return
     */
    @PostMapping(value = "/afterSale/search/{page}/{size}" )
    Result<QueryResult<AfterSale>> findPage(@RequestBody(required = false) AfterSale afterSale, @PathVariable("page") int page, @PathVariable("size") int size, @RequestParam(value = "fields", required = false) String fields);


    @PostMapping("/afterSale")
    Result<Boolean> add(@RequestBody AfterSale afterSale);

    @PostMapping("/afterSale/batch/delete")
    Result<Boolean> batchDelete(@RequestParam("opid") String opid);

    /***
     * 根据ID查询数据
     * @param id
     * @return
     */
    @GetMapping("/afterSale/{id}")
    Result<AfterSale> findById(@PathVariable("id") Long id);

    /***
     * 修改数据
     * @param afterSale
     * @return
     */
    @PutMapping(value="/afterSale/{id}")
    Result<Boolean> update(@RequestBody AfterSale afterSale, @PathVariable("id") Long id);


    /***
     * 根据ID删除数据
     * @param id
     * @return
     */
    @DeleteMapping(value = "/afterSale/{id}" )
    Result<Boolean> delete(@PathVariable("id") Long id);
}
