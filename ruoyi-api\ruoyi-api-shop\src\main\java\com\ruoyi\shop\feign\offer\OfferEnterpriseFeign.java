package com.ruoyi.shop.feign.offer;

import com.ruoyi.shop.entity.OfferEnterprise;
import com.ruoyi.shop.result.QueryResult; import com.ruoyi.shop.result.Result;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author:micah
 **/
public interface OfferEnterpriseFeign {
    /***
     * 多条件搜索数据
     * @param offerEnterprise
     * @return
     */
    @PostMapping(value = "/offer/enterprise/search" )
    Result<List<OfferEnterprise>> findList(@RequestBody OfferEnterprise offerEnterprise, @RequestParam(value = "fields", required = false) String fields);

    /***
     * 批量新增OfferEnterprise数据
     * @param offerEnterprise
     * @return
     */
    @PostMapping("/offer/enterprise/add")
    Result<Boolean> add(@RequestBody OfferEnterprise offerEnterprise);

    /***
     * 批量刪除OfferEnterprise数据
     * @param opid
     * @return
     */
    @PostMapping("/offer/enterprise/batch/delete")
    Result<Boolean> batchDelete(@RequestParam("opid") String opid);
}