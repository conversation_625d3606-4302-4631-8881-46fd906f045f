package com.ruoyi.shop.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author:micah
 **/

@TableName("tb_order_items")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderItems extends Model<OrderItems>{

	@TableId(value = "id", type = IdType.AUTO)
	private Long id;//id

	private Long order_id;//订单id

	private String order_no;//订单编号

	private Long product_id;//商品ID

	private Long classify_id;//一级分类ID

	private Long classify2_id;//二级分类ID

	private Long classify3_id;//三级分类ID

	private String product_no;//物料编号

	private String product_name;//物料名称

	private String specs;//型号规格

	private BigDecimal quantity;//数量

	private BigDecimal shiped_quantity;//已发数量

	private String unit;//货物单位

	private String brand;//品牌

	private BigDecimal tax_price;//目标含税单价

	private BigDecimal total_price;//含税总价

	private BigDecimal freight; //运费

	private BigDecimal tax_rate;//税率

	private String delivery_date;//期望交货日期

	private String pur_attachment;//采购商附件

	private String sup_attachment; //供应商附件

	private String delivered_date;//交货日期

	private String pack;//包装方式

	private String create_by;//创建者

	private Date create_time;//创建时间

	private String update_by;//更新者

	private Date update_time;//更新时间

	@TableField(exist = false)
	private String orderIds;//订单ids

	private String after_status;//售后状态  0-未申请 1-已申请
}
