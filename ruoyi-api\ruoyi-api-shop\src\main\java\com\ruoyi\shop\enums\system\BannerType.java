package com.ruoyi.shop.enums.system;

/**
 * 导航图枚举
 */
public enum BannerType {

    INFOR("INFOR","资讯");

    private final String key;
    private final String value;

    BannerType(String key, String value){
        this.key = key;
        this.value = value;
    }
    //根据key获取枚举
    public static BannerType getBannerType(String key){
        if(null == key){
            return null;
        }
        for(BannerType temp: BannerType.values()){
            if(temp.getKey().equals(key)){
                return temp;
            }
        }
        return null;
    }

    public String getKey() {
        return key;
    }
    public String getValue() {
        return value;
    }
}
