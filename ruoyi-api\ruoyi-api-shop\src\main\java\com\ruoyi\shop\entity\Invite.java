package com.ruoyi.shop.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @ClassName Invite
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/3/12
 * @Version 1.0
 */
@TableName("tb_invite")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Invite extends Model<Invite> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;//ID

    private Long enterprise_id;//企业ID

    private String enterprise_name;//企业名称

    private String remark;//备注

    private String status;//状态

    private String create_by;//创建者

    private Long create_by_id;//创建者ID

    private Date create_time;//创建时间

    private String update_by;//更新者

    private Date update_time;//更新时间

    @TableField(exist = false)
    private Enterprise enterprise;
}
