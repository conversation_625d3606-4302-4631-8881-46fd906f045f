package com.ruoyi.shop.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:micah
 **/

@TableName("tb_user_label_relation")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserLabelRelation extends Model<UserLabelRelation>{

	@TableId(value = "id", type = IdType.AUTO)
	private Long id;//主键id

	private Long user_id;//用户id

	private Long label_id;//标签ID


}
