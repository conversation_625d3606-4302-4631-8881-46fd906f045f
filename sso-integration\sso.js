/**
 * SSO单点登录前端集成
 */

import request from '@/utils/request'
import { getToken, setToken, removeToken } from '@/utils/auth'

// SSO相关API
const ssoApi = {
  // SSO登录
  ssoLogin: (token, redirect) => {
    return request({
      url: '/sso/login',
      method: 'get',
      params: { token, redirect }
    })
  },
  
  // 检查SSO状态
  checkSSOStatus: () => {
    return request({
      url: '/sso/status',
      method: 'get'
    })
  },
  
  // 获取主系统登录地址
  getMainSystemLoginUrl: (redirect) => {
    return request({
      url: '/sso/loginUrl',
      method: 'get',
      params: { redirect }
    })
  },
  
  // SSO登出
  ssoLogout: (ssoToken) => {
    return request({
      url: '/sso/logout',
      method: 'post',
      data: { ssoToken }
    })
  }
}

// SSO工具类
const SSOUtils = {
  
  /**
   * 检查URL中是否包含SSO Token
   */
  checkSSOTokenInUrl() {
    const urlParams = new URLSearchParams(window.location.search)
    return urlParams.get('token')
  },
  
  /**
   * 处理SSO登录
   */
  async handleSSOLogin() {
    const ssoToken = this.checkSSOTokenInUrl()
    if (!ssoToken) {
      return false
    }
    
    try {
      // 获取重定向地址
      const urlParams = new URLSearchParams(window.location.search)
      const redirect = urlParams.get('redirect')
      
      // 调用SSO登录接口
      const response = await ssoApi.ssoLogin(ssoToken, redirect)
      
      if (response.code === 200) {
        // 保存Token
        setToken(response.data.token)
        
        // 清除URL中的SSO参数
        this.clearSSOParamsFromUrl()
        
        // 重定向到目标页面
        if (redirect) {
          window.location.href = redirect
        } else {
          window.location.href = '/'
        }
        
        return true
      } else {
        console.error('SSO登录失败:', response.msg)
        return false
      }
    } catch (error) {
      console.error('SSO登录异常:', error)
      return false
    }
  },
  
  /**
   * 清除URL中的SSO参数
   */
  clearSSOParamsFromUrl() {
    const url = new URL(window.location)
    url.searchParams.delete('token')
    url.searchParams.delete('redirect')
    window.history.replaceState({}, document.title, url.toString())
  },
  
  /**
   * 跳转到主系统登录
   */
  async redirectToMainSystemLogin() {
    try {
      const currentUrl = window.location.href
      const response = await ssoApi.getMainSystemLoginUrl(currentUrl)
      
      if (response.code === 200) {
        window.location.href = response.data.loginUrl
      } else {
        console.error('获取主系统登录地址失败:', response.msg)
      }
    } catch (error) {
      console.error('跳转到主系统登录失败:', error)
    }
  },
  
  /**
   * 检查当前用户的SSO状态
   */
  async checkSSOStatus() {
    try {
      const response = await ssoApi.checkSSOStatus()
      return response.code === 200 && response.data.ssoValid
    } catch (error) {
      console.error('检查SSO状态失败:', error)
      return false
    }
  },
  
  /**
   * SSO登出
   */
  async ssoLogout() {
    try {
      const token = getToken()
      if (token) {
        await ssoApi.ssoLogout(token)
      }
      
      // 清除本地Token
      removeToken()
      
      // 跳转到主系统登录页面
      await this.redirectToMainSystemLogin()
    } catch (error) {
      console.error('SSO登出失败:', error)
      // 即使登出失败也要清除本地Token
      removeToken()
    }
  }
}

// Vue插件形式导出
const SSOPlugin = {
  install(Vue) {
    Vue.prototype.$sso = SSOUtils
    
    // 全局混入，在每个组件中都可以使用SSO功能
    Vue.mixin({
      created() {
        // 在应用启动时检查SSO登录
        if (this.$route.query.token) {
          this.$sso.handleSSOLogin()
        }
      }
    })
  }
}

export default SSOPlugin
export { SSOUtils, ssoApi }
