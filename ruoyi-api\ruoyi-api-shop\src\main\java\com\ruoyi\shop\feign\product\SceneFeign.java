package com.ruoyi.shop.feign.product;

import com.ruoyi.shop.entity.CreditRating;
import com.ruoyi.shop.entity.Scene;
import com.ruoyi.shop.result.QueryResult;
import com.ruoyi.shop.result.Result;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @ClassName SceneFeign
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/1/5
 * @Version 1.0
 */
public interface SceneFeign {

    @PostMapping(value = "/scene/search/{page}/{size}" )
    Result<QueryResult<Scene>> findPage(@RequestBody(required = false) Scene scene, @PathVariable("page") int page, @PathVariable("size") int size, @RequestParam(value = "fields", required = false) String fields);

    /**
     * 新增
     * @param scene
     * @return
     */
    @PostMapping(value = "/scene")
    Result<Boolean> add(@RequestBody Scene scene);

    @PostMapping("/scene/batch/delete")
    Result<Boolean> batchDelete(@RequestParam("opid") String opid);

    @GetMapping("/scene/{id}")
    Result<Scene> findById(@PathVariable("id") Long id);

    @PutMapping(value="/scene/{id}")
    Result<Boolean> update(@RequestBody Scene scene, @PathVariable("id") Long id);

    @DeleteMapping(value = "/scene/{id}" )
    Result<Boolean> delete(@PathVariable("id") Long id);

    @PostMapping(value = "/scene/search" )
    Result<List<Scene>> findList(@RequestBody(required = false) Scene scene, @RequestParam(value = "fields", required = false) String fields);
}